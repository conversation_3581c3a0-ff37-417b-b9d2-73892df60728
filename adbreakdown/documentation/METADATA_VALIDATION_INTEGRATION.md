# Metadata Validation Integration Guide

This guide shows how to integrate the new metadata validation system into your existing analysis pages.

## ✅ What's Been Created

### 1. **Core Files**
- **`/src/lib/prompts/metadataValidationPrompt.ts`** - AI prompt for search-based validation
- **`/src/app/api/analyses/[id]/validate-metadata/route.ts`** - API endpoint for validation
- **`/src/hooks/useMetadataValidation.ts`** - React hook for easy integration
- **`/src/components/analysis/MetadataValidationCard.tsx`** - Ready-to-use UI component

### 2. **Features**
- **Search-based validation** using Gemini's Google Search integration
- **Confidence scoring** for each metadata field (0-10 scale)
- **Discrepancy detection** with impact levels (high/medium/low)
- **Enhancement recommendations** for missing or incomplete data
- **Source verification** showing what search queries were used

## 🚀 Quick Integration

### Option 1: Add to Sidebar (Recommended)
Add the validation card to the existing sidebar in `SharedAnalysisPage.tsx`:

```tsx
// In SharedAnalysisPage.tsx, import the component
import MetadataValidationCard from '@/components/analysis/MetadataValidationCard'

// Add to the sidebar section (around line 1250-1360)
<div className="lg:col-span-1">
  <div className="lg:sticky lg:top-6 space-y-4">
    {/* Existing sidebar components */}
    <ScorecardSidebar parsedData={parsedData} />
    <ExecutiveBriefingCard parsedData={parsedData} />
    <MetadataSidebar parsedData={parsedData} youtubeMetadata={youtubeMetadata} videoMetadata={videoMetadata} />
    
    {/* NEW: Add metadata validation */}
    {!isFeaturedMode && parsedData?.metadata && (
      <MetadataValidationCard 
        analysisId={analysisId}
        currentMetadata={parsedData.metadata}
      />
    )}
    
    {/* Rest of sidebar */}
  </div>
</div>
```

### Option 2: Add to Tab System
Add as a new tab in the deep dive section:

```tsx
// In TabsComponent.tsx, add a new tab
const tabs = [
  // existing tabs...
  { id: 'validation', label: 'Metadata Validation', icon: Search }
]

// In the tab content section
{activeTab === 'validation' && (
  <MetadataValidationCard 
    analysisId={analysisId}
    currentMetadata={parsedData?.metadata}
  />
)}
```

## 🔧 API Usage Examples

### Basic Validation Call
```tsx
import { useMetadataValidation } from '@/hooks/useMetadataValidation'

const { validateMetadata, isValidating, validationResult, error } = useMetadataValidation()

// Trigger validation
const handleValidate = async () => {
  try {
    const result = await validateMetadata(analysisId)
    console.log('Validation result:', result)
  } catch (err) {
    console.error('Validation failed:', err)
  }
}
```

### Direct API Call
```tsx
const response = await fetch(`/api/analyses/${analysisId}/validate-metadata`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' }
})

const result = await response.json()
```

## 📊 Response Structure

The validation API returns a comprehensive result:

```json
{
  "message": "Metadata validation completed successfully",
  "analysis_id": "uuid",
  "slug": "analysis-slug",
  "validation_result": {
    "validation_summary": {
      "overall_accuracy_score": 8,
      "major_discrepancies_found": 2,
      "fields_enhanced": 3,
      "confidence_level": "high"
    },
    "validated_metadata": {
      "brand": {
        "value": "Corrected Brand Name",
        "confidence": 9,
        "changes_made": "Fixed spelling from 'Brane' to 'Brand'",
        "source_verification": "Official brand website and recent campaigns"
      }
      // ... other metadata fields
    },
    "discrepancy_report": [
      {
        "field": "celebrity",
        "original_value": "John Doe",
        "corrected_value": "John Smith",
        "discrepancy_type": "factual",
        "impact_level": "medium"
      }
    ],
    "enhancement_recommendations": [
      {
        "field": "agency",
        "recommendation": "Add creative agency information",
        "reasoning": "Found campaign attribution to XYZ Agency",
        "priority": "high"
      }
    ],
    "search_sources": [
      {
        "query_used": "Brand Name advertisement campaign 2024",
        "key_findings": "Recent campaign launched with celebrity endorsement",
        "reliability_score": 8
      }
    ]
  },
  "original_metadata": {
    // Original metadata for comparison
  }
}
```

## 🎯 Use Cases

### 1. **Quality Assurance**
- Validate metadata accuracy before publishing analyses
- Check for spelling errors in brand names
- Verify celebrity endorsement claims

### 2. **Data Enhancement**
- Fill in missing agency information
- Add parent company details
- Enhance geographic information

### 3. **Fact Checking**
- Verify campaign launch dates
- Confirm brand-celebrity partnerships
- Validate product categorization

### 4. **Database Maintenance**
- Standardize brand naming across analyses
- Maintain consistent taxonomy
- Improve search and filtering capabilities

## ⚙️ Configuration

### Environment Variables
The validation uses the same Gemini API key as other features:
- `NEXT_PUBLIC_GEMINI_API_KEY` - Required for AI validation

### Customization Options

#### Adjust Confidence Thresholds
```tsx
// In the component or hook
const getLowConfidenceFields = () => {
  return fields.filter(field => field.confidence < 7) // Adjust threshold
}
```

#### Modify Search Parameters
```tsx
// In the API route, adjust the search grounding config
tools: [{
  googleSearchRetrieval: {
    dynamicRetrievalConfig: {
      mode: 'MODE_DYNAMIC',
      dynamicThreshold: 0.7 // Adjust search sensitivity
    }
  }
}]
```

## 🔍 Testing

### Test with Existing Analysis
1. Navigate to any completed analysis page
2. Look for the "Metadata Validation" card in the sidebar
3. Click "Validate Metadata with Search"
4. Review the results and confidence scores

### Sample Test Data
```tsx
const sampleMetadata = {
  brand: "McDonalds", // Intentional misspelling
  product_category: "Fast Food",
  campaign_category: "Brand Building",
  celebrity: "Travis Scott",
  geography: "USA"
}
```

Expected validation might correct "McDonalds" to "McDonald's" and provide verification of the Travis Scott partnership.

## 🚨 Important Notes

### Rate Limiting
- The validation uses Gemini API calls with search grounding
- Consider implementing rate limiting for high-traffic usage
- Search API has usage quotas

### Error Handling
- The system gracefully handles API failures
- Provides fallback error messages
- Logs detailed error information for debugging

### Performance
- Validation typically takes 10-30 seconds due to search operations
- Consider showing progress indicators
- Cache results to avoid repeated validations

## 🔄 Future Enhancements

### Potential Improvements
1. **Auto-correction**: Automatically apply high-confidence corrections
2. **Batch validation**: Validate multiple analyses at once
3. **Historical tracking**: Track validation improvements over time
4. **Custom validation rules**: Add industry-specific validation logic
5. **Integration with external databases**: Connect to brand/agency databases

### Database Schema Updates
Consider adding these fields to track validation:
```sql
ALTER TABLE ad_analyses ADD COLUMN metadata_validation JSONB;
ALTER TABLE ad_analyses ADD COLUMN validation_score INTEGER;
ALTER TABLE ad_analyses ADD COLUMN last_validated_at TIMESTAMP;
```

This would allow you to:
- Store validation results for future reference
- Track validation scores over time
- Show when metadata was last validated
- Build analytics on metadata quality