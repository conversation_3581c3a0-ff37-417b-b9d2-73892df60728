-- V12: Add Private Analysis Support
-- This migration adds support for private analysis features

-- Add private analysis fields to ad_analyses table
ALTER TABLE ad_analyses ADD COLUMN IF NOT EXISTS is_private boolean DEFAULT false;
ALTER TABLE ad_analyses ADD COLUMN IF NOT EXISTS analysis_type varchar(20) DEFAULT 'public';

-- Add indexes for private analysis queries
CREATE INDEX IF NOT EXISTS idx_ad_analyses_is_private ON ad_analyses(is_private);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_analysis_type ON ad_analyses(analysis_type);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_user_private ON ad_analyses(user_id, is_private) WHERE is_private = true;

-- Create oauth_tokens table for storing user OAuth tokens
CREATE TABLE IF NOT EXISTS oauth_tokens (
    id bigint generated by default as identity primary key,
    user_id uuid references users(id) on delete cascade,
    provider varchar(50) not null,
    access_token text not null,
    refresh_token text,
    token_type varchar(20) default 'Bearer',
    expires_at timestamp with time zone not null,
    scope text,
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone default now(),
    
    -- Ensure one token per user per provider
    unique(user_id, provider)
);

-- Add indexes for oauth_tokens
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_user_provider ON oauth_tokens(user_id, provider);
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expires_at ON oauth_tokens(expires_at);

-- Add RLS policies for oauth_tokens
ALTER TABLE oauth_tokens ENABLE row level security;

-- Users can only access their own OAuth tokens
CREATE POLICY "Users can view own oauth tokens" ON oauth_tokens
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own oauth tokens" ON oauth_tokens
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own oauth tokens" ON oauth_tokens
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete own oauth tokens" ON oauth_tokens
    FOR DELETE USING (user_id = auth.uid());

-- Update RLS policies for ad_analyses to handle private analyses
-- Users can view their own private analyses
CREATE POLICY "Users can view own private analyses" ON ad_analyses
    FOR SELECT USING (
        (is_private = true AND user_id = auth.uid()) OR 
        (is_private = false OR is_private IS NULL)
    );

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_oauth_tokens_updated_at()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language plpgsql;

-- Trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_oauth_tokens_updated_at_trigger ON oauth_tokens;
CREATE TRIGGER update_oauth_tokens_updated_at_trigger
    BEFORE UPDATE ON oauth_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_oauth_tokens_updated_at();

-- Add comments for documentation
COMMENT ON TABLE oauth_tokens IS 'Stores OAuth tokens for external providers (Google, etc.)';
COMMENT ON COLUMN oauth_tokens.provider IS 'OAuth provider name (google, facebook, etc.)';
COMMENT ON COLUMN oauth_tokens.access_token IS 'OAuth access token for API calls';
COMMENT ON COLUMN oauth_tokens.refresh_token IS 'OAuth refresh token for token renewal';
COMMENT ON COLUMN oauth_tokens.scope IS 'OAuth scopes granted by the user';

COMMENT ON COLUMN ad_analyses.is_private IS 'Whether this analysis is private to the user';
COMMENT ON COLUMN ad_analyses.analysis_type IS 'Type of analysis: public, private, enterprise';

-- Create function to clean up expired tokens
CREATE OR REPLACE FUNCTION cleanup_expired_oauth_tokens()
RETURNS void AS $$
BEGIN
    DELETE FROM oauth_tokens 
    WHERE expires_at < now() - interval '7 days';
END;
$$ language plpgsql;