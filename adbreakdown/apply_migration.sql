-- Essential migration for private analysis support
-- Run this in your Supabase SQL Editor

-- Add private analysis fields to ad_analyses table
ALTER TABLE ad_analyses ADD COLUMN IF NOT EXISTS is_private boolean DEFAULT false;
ALTER TABLE ad_analyses ADD COLUMN IF NOT EXISTS analysis_type varchar(20) DEFAULT 'public';

-- Add indexes for private analysis queries
CREATE INDEX IF NOT EXISTS idx_ad_analyses_is_private ON ad_analyses(is_private);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_analysis_type ON ad_analyses(analysis_type);

-- Create oauth_tokens table for storing user OAuth tokens
CREATE TABLE IF NOT EXISTS oauth_tokens (
    id bigint generated by default as identity primary key,
    user_id uuid references users(id) on delete cascade,
    provider varchar(50) not null,
    access_token text not null,
    refresh_token text,
    token_type varchar(20) default 'Bearer',
    expires_at timestamp with time zone not null,
    scope text,
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone default now(),
    
    -- Ensure one token per user per provider
    unique(user_id, provider)
);

-- Add indexes for oauth_tokens
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_user_provider ON oauth_tokens(user_id, provider);
CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expires_at ON oauth_tokens(expires_at);