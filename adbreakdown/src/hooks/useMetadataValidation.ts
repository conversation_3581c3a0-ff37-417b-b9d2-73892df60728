import { useState, useCallback } from 'react';

export interface ValidationSummary {
  overall_accuracy_score: number;
  major_discrepancies_found: number;
  fields_enhanced: number;
  confidence_level: 'high' | 'medium' | 'low';
}

export interface MetadataField {
  value: string | null;
  confidence: number;
  changes_made: string;
  source_verification: string;
}

export interface ValidationResult {
  validation_summary: ValidationSummary;
  validated_metadata: {
    ad_title: MetadataField;
    brand: MetadataField;
    product_category: MetadataField;
    campaign_category: MetadataField;
    parent_entity: MetadataField;
    celebrity: <PERSON>adataField;
    geography: MetadataField;
    agency: MetadataField;
    runtime: MetadataField;
  };
  enhancement_recommendations: Array<{
    field: string;
    recommendation: string;
    reasoning: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  discrepancy_report: Array<{
    field: string;
    original_value: string;
    corrected_value: string;
    discrepancy_type: 'spelling' | 'factual' | 'missing' | 'incorrect';
    impact_level: 'high' | 'medium' | 'low';
  }>;
  search_sources: Array<{
    query_used: string;
    key_findings: string;
    reliability_score: number;
  }>;
}

export interface ValidationResponse {
  message: string;
  analysis_id: string;
  slug: string;
  validation_result: ValidationResult;
  original_metadata: any;
  search_grounding_used: boolean;
}

export const useMetadataValidation = () => {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const validateMetadata = useCallback(async (analysisId: string) => {
    if (isValidating) {
      console.log('🚫 Validation already in progress, ignoring new request');
      return;
    }

    console.log('🔍 Starting metadata validation for:', analysisId);
    setIsValidating(true);
    setError(null);
    setValidationResult(null);

    try {
      console.log('📡 Making validation request...');
      const response = await fetch(`/api/analyses/${analysisId}/validate-metadata`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('📡 Validation response received:', { 
        ok: response.ok, 
        status: response.status 
      });

      if (!response.ok) {
        const err = await response.json().catch(() => ({ 
          error: 'Failed to validate metadata' 
        }));
        throw new Error(err.error);
      }

      const result: ValidationResponse = await response.json();
      console.log('✅ Validation completed successfully');
      console.log('📊 Validation summary:', result.validation_result.validation_summary);
      
      setValidationResult(result);
      return result;

    } catch (err: any) {
      console.error('❌ Validation error:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsValidating(false);
    }
  }, [isValidating]);

  const resetValidation = useCallback(() => {
    setIsValidating(false);
    setValidationResult(null);
    setError(null);
  }, []);

  // Helper function to get significant discrepancies
  const getSignificantDiscrepancies = useCallback(() => {
    if (!validationResult) return [];
    
    return validationResult.validation_result.discrepancy_report.filter(
      discrepancy => discrepancy.impact_level === 'high' || discrepancy.impact_level === 'medium'
    );
  }, [validationResult]);

  // Helper function to get high-priority recommendations
  const getHighPriorityRecommendations = useCallback(() => {
    if (!validationResult) return [];
    
    return validationResult.validation_result.enhancement_recommendations.filter(
      rec => rec.priority === 'high'
    );
  }, [validationResult]);

  // Helper function to get low-confidence fields
  const getLowConfidenceFields = useCallback(() => {
    if (!validationResult) return [];
    
    const metadata = validationResult.validation_result.validated_metadata;
    const lowConfidenceFields = [];
    
    for (const [fieldName, fieldData] of Object.entries(metadata)) {
      if (fieldData.confidence < 6) { // Less than 6/10 confidence
        lowConfidenceFields.push({
          field: fieldName,
          confidence: fieldData.confidence,
          value: fieldData.value
        });
      }
    }
    
    return lowConfidenceFields;
  }, [validationResult]);

  return {
    // State
    isValidating,
    validationResult,
    error,
    
    // Actions
    validateMetadata,
    resetValidation,
    
    // Helper functions
    getSignificantDiscrepancies,
    getHighPriorityRecommendations,
    getLowConfidenceFields,
  };
};