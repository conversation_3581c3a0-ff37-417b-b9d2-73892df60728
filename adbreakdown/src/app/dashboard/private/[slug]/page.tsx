'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Shield, ArrowLeft, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import { AnalysisSections } from '@/components/ui/sectioned-markdown'
import Link from 'next/link'
import Image from 'next/image'

interface PrivateAnalysis {
  id: string
  slug: string
  title: string
  inferred_brand: string
  youtube_url: string
  youtube_video_id: string
  status: string
  analysis_type: string
  is_private: boolean
  created_at: string
  thumbnail_url?: string
  duration_seconds?: number
  marketing_analysis?: string | any
  analysis_completed_at?: string
  overall_sentiment?: number
}

export default function PrivateAnalysisPage() {
  const params = useParams()
  const router = useRouter()
  const { isAuthenticated, loading: authLoading } = useAuth()
  const [analysis, setAnalysis] = useState<PrivateAnalysis | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchAnalysis = async () => {
      if (!isAuthenticated || !params.slug) return

      try {
        setLoading(true)
        const response = await fetch(`/api/analyses/private/${params.slug}`)
        
        if (response.status === 404) {
          setError('Private analysis not found or you do not have access to it.')
          return
        }
        
        if (!response.ok) {
          throw new Error('Failed to fetch analysis')
        }

        const data = await response.json()
        setAnalysis(data.analysis)
      } catch (err: any) {
        console.error('Error fetching private analysis:', err)
        setError(err.message || 'Failed to load analysis')
      } finally {
        setLoading(false)
      }
    }

    if (!authLoading) {
      fetchAnalysis()
    }
  }, [isAuthenticated, authLoading, params.slug])

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p>Loading private analysis...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">Authentication Required</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4">Please sign in to access private analyses.</p>
            <Link href="/sign-in">
              <Button>Sign In</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="mb-6">
            <Button 
              variant="ghost" 
              onClick={() => router.push('/dashboard')}
              className="mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <Card className="border-red-200">
            <CardHeader>
              <div className="flex items-center gap-3">
                <AlertCircle className="h-6 w-6 text-red-600" />
                <CardTitle className="text-red-900">Analysis Not Found</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-red-700 mb-4">{error}</p>
              <Link href="/dashboard">
                <Button>Return to Dashboard</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!analysis) {
    return null
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />
      case 'processing':
        return <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Analysis Complete'
      case 'pending':
        return 'Analysis Pending'
      case 'processing':
        return 'Processing...'
      default:
        return status
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/dashboard')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          
          <div className="flex items-center gap-3 mb-2">
            <Shield className="h-8 w-8 text-purple-600" />
            <h1 className="text-3xl font-bold text-gray-900">Private Analysis</h1>
            <div className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">
              PRE-LAUNCH
            </div>
          </div>
          <p className="text-gray-600">
            Confidential analysis for pre-launch optimization
          </p>
        </div>

        {/* Analysis Details */}
        <div className="grid gap-6 mb-8">
          <Card className="border-purple-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(analysis.status)}
                  <div>
                    <CardTitle>{analysis.title}</CardTitle>
                    <CardDescription>{analysis.inferred_brand}</CardDescription>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {getStatusText(analysis.status)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(analysis.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {analysis.thumbnail_url && (
                  <div className="lg:col-span-1">
                    <Image 
                      src={analysis.thumbnail_url} 
                      alt={analysis.title}
                      width={700} // Assuming a reasonable default width
                      height={400} // Assuming a reasonable default height
                      className="w-full h-auto rounded-lg shadow-sm"
                    />
                  </div>
                )}
                <div className={analysis.thumbnail_url ? "lg:col-span-2" : "lg:col-span-3"}>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Video URL</label>
                      <div className="mt-1">
                        <Link 
                          href={analysis.youtube_url} 
                          target="_blank" 
                          className="text-blue-600 hover:text-blue-800 text-sm break-all"
                        >
                          {analysis.youtube_url}
                        </Link>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">Analysis Type</label>
                        <div className="mt-1 text-sm font-medium text-purple-700 capitalize">
                          {analysis.analysis_type}
                        </div>
                      </div>
                      {analysis.duration_seconds && (
                        <div>
                          <label className="text-sm font-medium text-gray-600">Duration</label>
                          <div className="mt-1 text-sm">
                            {Math.floor(analysis.duration_seconds / 60)}:{(analysis.duration_seconds % 60).toString().padStart(2, '0')}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Analysis Results */}
          {analysis.status === 'pending' && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader>
                <CardTitle className="text-yellow-800">Analysis in Queue</CardTitle>
                <CardDescription className="text-yellow-700">
                  Your private analysis is pending. This will use our specialized pre-launch analysis prompt 
                  to provide strategic recommendations for optimizing your ad before it goes live.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-yellow-100 p-4 rounded-lg">
                  <h4 className="font-medium text-yellow-800 mb-2">What You'll Get:</h4>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• Pre-launch optimization recommendations</li>
                    <li>• Performance bottleneck identification</li>
                    <li>• Audience targeting suggestions</li>
                    <li>• Risk assessment and mitigation</li>
                    <li>• Competitive positioning analysis</li>
                    <li>• Go/No-Go recommendation</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          )}

          {analysis.status === 'completed' && (
            <div>
              {/* Analysis Header */}
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Pre-Launch Analysis Results
                  </CardTitle>
                  <CardDescription>
                    Strategic recommendations for optimizing your ad before launch
                  </CardDescription>
                </CardHeader>
              </Card>

              {/* Sectioned Analysis Content */}
              {analysis.marketing_analysis ? (
                <div className="max-w-none">
                  {typeof analysis.marketing_analysis === 'string' ? (
                    <AnalysisSections 
                      content={analysis.marketing_analysis}
                      className="analysis-sections"
                    />
                  ) : (
                    <Card className="border-amber-200 bg-amber-50">
                      <CardHeader>
                        <CardTitle className="text-amber-800">Raw Analysis Data</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <pre className="text-sm text-gray-700 overflow-x-auto bg-white p-4 rounded border">
                          {JSON.stringify(analysis.marketing_analysis, null, 2)}
                        </pre>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : (
                <Card className="border-yellow-200 bg-yellow-50">
                  <CardContent className="text-center py-8">
                    <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">Analysis completed but no results found</p>
                    <p className="text-sm text-gray-500">The analysis may have encountered an issue during processing.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {analysis.status === 'processing' && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-blue-800 flex items-center gap-2">
                  <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full" />
                  Analysis in Progress
                </CardTitle>
                <CardDescription className="text-blue-700">
                  Your private analysis is currently being processed using our advanced AI models. 
                  This typically takes 2-5 minutes.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-100 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-800 mb-2">Processing Steps:</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Video content analysis and extraction</li>
                    <li>• AI-powered strategic assessment</li>
                    <li>• Competitive landscape evaluation</li>
                    <li>• Performance predictions and recommendations</li>
                  </ul>
                  <p className="text-xs text-blue-600 mt-3">
                    You can safely close this page - we'll save your results and you can return anytime.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {analysis.status === 'failed' && (
            <Card className="border-red-200 bg-red-50">
              <CardHeader>
                <CardTitle className="text-red-800 flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  Analysis Failed
                </CardTitle>
                <CardDescription className="text-red-700">
                  There was an error processing your private analysis. Please try creating a new analysis.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-red-100 p-4 rounded-lg">
                  <p className="text-sm text-red-700 mb-3">
                    This could be due to video access restrictions, processing errors, or temporary service issues.
                  </p>
                  <Link href="/dashboard">
                    <Button variant="outline" size="sm">
                      Create New Analysis
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}