'use client'

import { useEffect, useState } from 'react'
import { useUser, useAuth } from '@clerk/nextjs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Shield, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function ConnectGooglePage() {
  const { user, isLoaded } = useUser()
  const router = useRouter()
  const [connecting, setConnecting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  // Check if user already has Google connected
  const hasGoogleAuth = user?.externalAccounts?.some(
    account => account.provider === 'google'
  )

  const handleConnectGoogle = async () => {
    try {
      setConnecting(true)
      setError('')
      
      if (!user) {
        throw new Error('User not found')
      }

      // Create external account connection
      const result = await user.createExternalAccount({
        strategy: 'oauth_google',
        redirectUrl: `${window.location.origin}/auth/connect/google?success=true`
      })
      
      console.log('Google connection result:', result)
      
    } catch (err: any) {
      console.error('Error connecting Google:', err)
      setError(err.message || 'Failed to connect Google account')
      setConnecting(false)
    }
  }

  // Handle success redirect
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('success') === 'true') {
      setSuccess(true)
      setConnecting(false)
      
      // Reload user data to get updated external accounts
      if (user) {
        user.reload()
      }
      
      // Redirect back to dashboard after 2 seconds
      setTimeout(() => {
        router.push('/dashboard')
      }, 2000)
    }
  }, [router, user])

  const handleReturnToDashboard = () => {
    router.push('/dashboard')
  }

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">Authentication Required</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4">Please sign in to connect your Google account.</p>
            <Link href="/sign-in">
              <Button>Sign In</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        <div className="mb-6">
          <Button 
            variant="ghost" 
            onClick={handleReturnToDashboard}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Connect Google Account
          </h1>
          <p className="text-gray-600">
            Connect your Google account to access private and unlisted YouTube videos for analysis.
          </p>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Shield className="h-6 w-6 text-purple-600" />
              <div>
                <CardTitle>YouTube Private Access</CardTitle>
                <CardDescription>
                  Required for analyzing unlisted and private YouTube videos
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {success ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-green-800">Successfully Connected!</h4>
                    <p className="text-sm text-green-700 mt-1">
                      Your Google account has been connected. You can now analyze private and unlisted YouTube videos.
                    </p>
                    <p className="text-xs text-green-600 mt-2">
                      Redirecting you back to the dashboard...
                    </p>
                  </div>
                </div>
              </div>
            ) : hasGoogleAuth ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-800">Already Connected</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Your Google account is already connected and ready for private video analysis.
                    </p>
                    <Button
                      onClick={handleReturnToDashboard}
                      className="mt-3"
                      size="sm"
                    >
                      Return to Dashboard
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-800">Permissions Required</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        We need access to your YouTube account to analyze private and unlisted videos. This will:
                      </p>
                      <ul className="text-sm text-yellow-700 mt-2 space-y-1 ml-4">
                        <li>• Allow access to your unlisted YouTube videos</li>
                        <li>• Enable private video analysis features</li>
                        <li>• Keep all analysis results private to your account</li>
                        <li>• Never modify or share your videos</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-red-800">Connection Failed</h4>
                        <p className="text-sm text-red-700 mt-1">{error}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex justify-center">
                  <Button
                    onClick={handleConnectGoogle}
                    disabled={connecting}
                    className="w-full max-w-sm bg-[#4285f4] hover:bg-[#3367d6] text-white"
                  >
                    {connecting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Connecting...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                          <path
                            fill="currentColor"
                            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                          />
                          <path
                            fill="currentColor"
                            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                          />
                          <path
                            fill="currentColor"
                            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                          />
                          <path
                            fill="currentColor"
                            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                          />
                        </svg>
                        Connect with Google
                      </>
                    )}
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}