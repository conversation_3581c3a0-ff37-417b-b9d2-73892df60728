import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// Check Google OAuth connection status
export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()
    
    // Get user's database ID
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { connected: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Check for existing OAuth tokens
    const { data: tokenData, error: tokenError } = await supabase
      .from('oauth_tokens')
      .select('expires_at, scope, created_at')
      .eq('user_id', user.id)
      .eq('provider', 'google')
      .single()

    if (tokenError || !tokenData) {
      return NextResponse.json({
        connected: false,
        message: 'No Google account connected'
      })
    }

    // Check if token is expired
    const now = new Date()
    const expiresAt = new Date(tokenData.expires_at)
    const isExpired = now >= expiresAt

    return NextResponse.json({
      connected: true,
      expired: isExpired,
      expiresAt: tokenData.expires_at,
      scope: tokenData.scope,
      connectedAt: tokenData.created_at,
      message: isExpired ? 'Token expired, will refresh automatically' : 'Google account connected'
    })

  } catch (error) {
    console.error('❌ Error checking OAuth status:', error)
    return NextResponse.json(
      { error: 'Failed to check status' },
      { status: 500 }
    )
  }
}