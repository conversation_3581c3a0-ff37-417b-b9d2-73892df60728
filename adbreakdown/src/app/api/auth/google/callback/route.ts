import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// Handle Google OAuth callback
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')

    // Check for OAuth errors
    if (error) {
      console.error('❌ OAuth error:', error)
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_error=${error}`)
    }

    if (!code || !state) {
      console.error('❌ Missing OAuth parameters')
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_error=missing_params`)
    }

    // Decode and validate state
    let stateData: { userId: string; timestamp: number; nonce: string }
    try {
      stateData = JSON.parse(Buffer.from(state, 'base64').toString())
    } catch (err) {
      console.error('❌ Invalid state parameter')
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_error=invalid_state`)
    }

    // Check state timestamp (should be within 10 minutes)
    if (Date.now() - stateData.timestamp > 10 * 60 * 1000) {
      console.error('❌ Expired OAuth state')
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_error=expired_state`)
    }

    const { userId } = stateData

    // Exchange code for tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/auth/google/callback`,
      }),
    })

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json()
      console.error('❌ Token exchange failed:', errorData)
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_error=token_exchange_failed`)
    }

    const tokens = await tokenResponse.json()
    console.log('✅ Received tokens for user:', userId)

    // Get user info from Google
    const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`
      }
    })

    let userInfo = null
    if (userInfoResponse.ok) {
      userInfo = await userInfoResponse.json()
      console.log('📋 User info:', { email: userInfo.email, name: userInfo.name })
    }

    // Store tokens in database
    const supabase = createServerSupabaseClient()
    
    // Get user's database ID
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      console.error('❌ User not found:', userError)
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_error=user_not_found`)
    }

    // Calculate expiration time
    const expiresAt = new Date(Date.now() + tokens.expires_in * 1000).toISOString()

    // Upsert OAuth tokens
    const { error: tokenError } = await supabase
      .from('oauth_tokens')
      .upsert({
        user_id: user.id,
        provider: 'google',
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token || null,
        token_type: tokens.token_type || 'Bearer',
        expires_at: expiresAt,
        scope: tokens.scope || 'https://www.googleapis.com/auth/youtube.readonly',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (tokenError) {
      console.error('❌ Failed to store tokens:', tokenError)
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_error=storage_failed`)
    }

    console.log('✅ Successfully stored OAuth tokens for user:', userId)

    // Redirect back to dashboard with success
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_success=true`)

  } catch (error) {
    console.error('❌ Error in OAuth callback:', error)
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/dashboard?oauth_error=internal_error`)
  }
}