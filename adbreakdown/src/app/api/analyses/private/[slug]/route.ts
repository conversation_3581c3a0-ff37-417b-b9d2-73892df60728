import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/analyses/private/[slug] - Get private analysis by slug
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get the private analysis by slug and ensure it belongs to the user
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        slug,
        youtube_url,
        youtube_video_id,
        title,
        inferred_brand,
        duration_seconds,
        thumbnail_url,
        status,
        analysis_type,
        is_private,
        is_public,
        created_at,
        updated_at,
        analysis_completed_at,
        marketing_analysis,
        overall_sentiment,
        summary,
        key_themes
      `)
      .eq('slug', slug)
      .eq('user_id', user.id)
      .eq('is_private', true)
      .single()

    if (analysisError || !analysis) {
      console.log('Private analysis not found:', { slug, userId: user.id, error: analysisError })
      return NextResponse.json(
        { error: 'Private analysis not found or you do not have access to it' },
        { status: 404 }
      )
    }

    // Ensure this is actually a private analysis
    if (!analysis.is_private) {
      return NextResponse.json(
        { error: 'Analysis is not private' },
        { status: 403 }
      )
    }

    console.log('✅ Retrieved private analysis:', analysis.id, 'for user:', user.id)

    return NextResponse.json({
      success: true,
      analysis
    })

  } catch (error) {
    console.error('❌ Error fetching private analysis:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}