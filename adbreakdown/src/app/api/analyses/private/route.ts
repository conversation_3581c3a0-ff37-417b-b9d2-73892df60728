import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { 
  parseYouTubeUrl, 
  generateUniqueSlug, 
  isValidYouTubeUrl 
} from '@/lib/slug-utils'
import { validateYouTubeUrlWithAuth } from '@/lib/youtube-validator-private'
import { CacheService } from '@/lib/cache'
import { PRE_LAUNCH_ANALYSIS_PROMPT } from '@/lib/prompts/preLaunchAnalysisPrompt'
import { VertexAI } from '@google-cloud/vertexai'

// Processing function for private analysis
async function triggerPrivateAnalysisProcessing(analysis: any, youtubeUrl: string, supabase: any) {
  
  try {
    console.log('🤖 Starting LLM processing for private analysis:', analysis.id)

    // Initialize Vertex AI with authentication (copied from working trigger-vertex-analysis route)
    const projectId = process.env.GOOGLE_PROJECT_ID
    const serviceAccountKey = process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY
    
    console.log('🔧 Google Cloud Authentication Check (Private):', {
      hasProjectId: !!projectId,
      hasServiceAccountKey: !!serviceAccountKey,
      serviceAccountKeyLength: serviceAccountKey?.length || 0,
      nodeEnv: process.env.NODE_ENV
    })
    
    if (!projectId) {
      console.error('❌ Missing GOOGLE_PROJECT_ID environment variable')
      throw new Error('Google Cloud Project ID not configured')
    }

    let vertex_ai: VertexAI
    
    // Check if we have service account key for production
    if (serviceAccountKey) {
      try {
        console.log('🔑 Attempting to parse service account key...')
        let credentials;

        // The service account key might be a raw JSON string or a Base64 encoded string.
        // We'll try parsing it as raw JSON first.
        try {
          credentials = JSON.parse(serviceAccountKey);
          console.log('✅ Successfully parsed raw JSON from service account key.');
        } catch (rawParseError) {
          // If raw parsing fails, assume it's Base64 encoded, which is a common and robust way to store JSON in env vars.
          console.log('⚠️ Raw JSON parse failed, attempting Base64 decoding...');
          const decodedKey = Buffer.from(serviceAccountKey, 'base64').toString('utf-8');
          credentials = JSON.parse(decodedKey);
          console.log('✅ Successfully parsed Base64 decoded service account key.');
        }
        
        vertex_ai = new VertexAI({
          project: projectId,
          location: 'us-central1',
          googleAuthOptions: {
            credentials
          }
        })
        console.log('🔐 Successfully initialized Vertex AI with service account key authentication')
      } catch (error: any) {
        console.error('❌ Failed to parse service account key (tried raw and Base64):', {
          parseError: error.message,
          keyPreview: serviceAccountKey.substring(0, 100) + '...'
        })
        throw new Error(`Invalid service account credentials: ${error.message}`)
      }
    } else {
      console.log('⚠️ No service account key found, using default authentication (this will fail in production)')
      // Use default authentication (works in dev with gcloud auth)
      vertex_ai = new VertexAI({
        project: projectId,
        location: 'us-central1',
      })
      console.log('🔐 Initialized Vertex AI with default authentication (dev only)')
    }

    // Enhanced generation config (matching working route)
    const generationConfig = {
      temperature: 0.9,
      topP: 0.95,
      maxOutputTokens: 65535,
    };

    // Safety settings (matching working route - all off)
    const safetySettings = [
      {
        category: 'HARM_CATEGORY_HATE_SPEECH' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT' as any, 
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT' as any,
        threshold: 'BLOCK_NONE' as any
      }
    ];

    const model = vertex_ai.getGenerativeModel({ 
      model: 'gemini-2.5-pro',
      generationConfig,
      safetySettings
    })

    console.log('📝 Using PRE_LAUNCH_ANALYSIS_PROMPT for private analysis')
    const promptContent = PRE_LAUNCH_ANALYSIS_PROMPT
    
    console.log('📊 PRIVATE ANALYSIS CONFIG:', {
      model: 'gemini-2.5-pro',
      temperature: 0.9,
      topP: 0.95,
      maxTokens: 65535,
      safetySettings: 'All disabled',
      promptLength: promptContent.length,
      analysisMethod: 'Vertex AI Private Analysis'
    })
    
    const systemInstruction = {
      role: "system",
      parts: [{ text: promptContent }],
    };

    const videoPart = {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: youtubeUrl,
      },
    };

    // Vertex AI requires a text part alongside a file part in multimodal requests.
    const textPart = {
      text: `Please analyze the provided video advertisement using the pre-launch analysis framework. Focus on providing actionable recommendations for optimizing this ad before it launches publicly.

**Video Information:**
- Title: ${analysis.title || 'Unknown Title'}
- Brand/Channel: ${analysis.inferred_brand || 'Unknown Brand'}
- Duration: ${analysis.duration_seconds || 0} seconds
- YouTube URL: ${analysis.youtube_url}
- Video ID: ${analysis.youtube_video_id}`
    };

    console.log('🚀 Sending request to Vertex AI for private analysis')

    // Generate the analysis using proper multimodal request format
    const result = await model.generateContent({
      contents: [{ role: "user", parts: [textPart, videoPart] }],
      systemInstruction
      // generationConfig and safetySettings are already configured in the model
    });

    const response = result.response;

    // Extract text response (matching working route pattern)
    if (!response.candidates?.length || !response.candidates[0].content?.parts[0]?.text) {
      console.error("❌ Invalid response structure from Vertex AI:", JSON.stringify(response, null, 2));
      throw new Error("Received an invalid or empty text response from Vertex AI.");
    }

    const analysisText = response.candidates[0].content.parts[0].text;

    console.log('✅ Received analysis from Vertex AI, length:', analysisText.length)

    // Update the analysis record with results
    const { error: updateError } = await supabase
      .from('ad_analyses')
      .update({
        marketing_analysis: analysisText,
        status: 'completed',
        analysis_completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', analysis.id)

    if (updateError) {
      throw new Error(`Failed to save analysis results: ${updateError.message}`)
    }

    console.log('✅ Private analysis completed successfully:', analysis.id)

  } catch (error) {
    console.error('❌ Error in private analysis processing:', error)
    
    // Mark as failed
    await supabase
      .from('ad_analyses')
      .update({ 
        status: 'failed',
        updated_at: new Date().toISOString()
      })
      .eq('id', analysis.id)
    
    throw error
  }
}

// POST /api/analyses/private - Create private analysis
export async function POST(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Use service role client for user operations (bypasses RLS)
    const supabase = createServerSupabaseClient()

    // Get user from database
    console.log('🔍 Looking for user with Clerk ID:', userId)
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()
      
    console.log('🔍 User query result:', { user, userError })

    if (userError || !user) {
      console.log('🔧 User not found in database, creating user for Clerk ID:', userId)
      
      // Try to create user automatically
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          clerk_id: userId,
          email: '<EMAIL>',
          first_name: 'User',
          last_name: 'Auto'
        })
        .select()
        .single()

      if (createError) {
        console.error('❌ Error creating user:', createError)
        return NextResponse.json(
          { error: 'Failed to create user account' },
          { status: 500 }
        )
      }

      user = newUser
      console.log('✅ Created new user:', user)
    }

    // Skip credit check for now (credits column doesn't exist)
    console.log('💳 Skipping credit check - credits system not implemented yet')

    const { youtubeUrl } = await req.json()

    if (!youtubeUrl) {
      return NextResponse.json(
        { error: 'YouTube URL is required' },
        { status: 400 }
      )
    }

    // Basic URL validation first
    if (!isValidYouTubeUrl(youtubeUrl)) {
      return NextResponse.json(
        { error: 'Invalid YouTube URL format' },
        { status: 400 }
      )
    }

    // Get user's OAuth tokens for YouTube access
    let authToken = null
    try {
      const { getUserYouTubeToken } = await import('@/lib/youtube-validator-private')
      authToken = await getUserYouTubeToken(user.id)
      console.log('🔑 Retrieved OAuth token for private analysis:', authToken ? 'Found' : 'Not found')
    } catch (tokenError) {
      console.warn('⚠️ Could not retrieve OAuth token:', tokenError)
      // Continue without token - will use API key
    }
    
    // Validate YouTube URL with OAuth token (if available)
    console.log('🔍 Validating YouTube URL:', youtubeUrl)
    const validation = await validateYouTubeUrlWithAuth(youtubeUrl, authToken)
    console.log('✅ Validation result:', { isValid: validation.isValid, error: validation.error })
    
    if (!validation.isValid) {
      if (validation.error?.includes('OAuth')) {
        return NextResponse.json(
          { 
            error: 'Authentication required',
            message: 'Please connect your Google account to analyze this video.',
            requiresAuth: true
          },
          { status: 401 }
        )
      }
      
      if (validation.error?.includes('duration')) {
        // For videos too long, we can still create a pending analysis for admin review
        // But for private analysis, we might want different handling
        return NextResponse.json(
          { 
            error: validation.error,
            metadata: validation.metadata,
            duration: validation.duration,
            videoId: validation.videoId
          },
          { status: 413 } // Request Entity Too Large
        )
      }

      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      )
    }

    // Check if user already has a private analysis for this video
    const { data: existingAnalysis } = await supabase
      .from('ad_analyses')
      .select('id, slug')
      .eq('youtube_video_id', validation.videoId)
      .eq('user_id', user.id)
      .eq('is_private', true)
      .single()

    if (existingAnalysis) {
      return NextResponse.json(
        {
          message: 'Private analysis already exists for this video',
          analysis: {
            id: existingAnalysis.id,
            slug: existingAnalysis.slug
          },
          redirect: `/dashboard/private/${existingAnalysis.slug}`
        },
        { status: 200 }
      )
    }

    // Generate unique slug
    const slug = await generateUniqueSlug(
      validation.metadata?.channelTitle || 'Unknown',
      validation.metadata?.title || 'Private Analysis',
      validation.videoId || 'unknown',
      supabase,
      'private-analysis'
    )

    console.log('📝 Creating private analysis with data:', {
      user_id: user.id,
      youtube_url: validation.normalizedUrl,
      youtube_video_id: validation.videoId,
      slug,
      title: validation.metadata?.title,
      is_private: true
    })

    // Create analysis record using actual schema columns
    const { data: analysis, error: insertError } = await supabase
      .from('ad_analyses')
      .insert({
        user_id: user.id,
        youtube_url: validation.normalizedUrl || youtubeUrl,
        youtube_video_id: validation.videoId,
        slug,
        title: validation.metadata?.title || 'Private Analysis',
        inferred_brand: validation.metadata?.channelTitle || 'Unknown',
        duration_seconds: validation.duration?.seconds || 0,
        thumbnail_url: validation.metadata?.thumbnailUrl,
        status: 'pending',
        is_public: false,
        is_private: true,
        analysis_type: 'private'
      })
      .select()
      .single()

    if (insertError) {
      console.error('❌ Error creating private analysis:', insertError)
      return NextResponse.json(
        { error: 'Failed to create private analysis' },
        { status: 500 }
      )
    }

    // Skip credit deduction for now (credits system not implemented)
    console.log('💳 Skipping credit deduction - credits system not implemented yet')

    console.log('✅ Created private analysis:', analysis)

    // Clear relevant caches (skip if cache not available)
    try {
      const cache = new CacheService()
      await Promise.all([
        cache.delete(`user-analyses-${user.id}`),
        cache.delete(`user-stats-${user.id}`)
      ])
    } catch (cacheError) {
      console.log('⚠️ Cache not available, skipping cache clear:', cacheError.message)
    }

    // Step 2: Trigger private analysis processing (similar to trigger-initial-analysis)
    console.log('🚀 Step 2: Triggering private analysis processing for ID:', analysis.id)
    try {
      // Trigger the LLM processing directly using the same approach as trigger-initial-analysis
      await triggerPrivateAnalysisProcessing(analysis, youtubeUrl, supabase)
      console.log('✅ Private analysis processing completed successfully')
    } catch (triggerError) {
      console.warn('⚠️ Error in private analysis processing:', triggerError)
      // Mark as failed but don't fail the main request
      await supabase
        .from('ad_analyses')
        .update({ 
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', analysis.id)
        .catch(updateError => console.error('Failed to mark as failed:', updateError))
    }

    // Return success response
    return NextResponse.json({
      success: true,
      analysis: {
        id: analysis.id,
        slug: analysis.slug,
        title: analysis.title,
        status: analysis.status
      },
      redirect: `/dashboard/private/${analysis.slug}`,
      message: 'Private analysis created and processing initiated'
    })

  } catch (error) {
    console.error('❌ Error in private analysis creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/analyses/private - Get user's private analyses
export async function GET(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get user's private analyses
    const { data: analyses, error: analysesError } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        slug,
        youtube_url,
        youtube_video_id,
        title,
        inferred_brand,
        status,
        duration_seconds,
        thumbnail_url,
        overall_sentiment,
        created_at,
        analysis_completed_at,
        analysis_type
      `)
      .eq('user_id', user.id)
      .eq('is_private', true)
      .order('created_at', { ascending: false })

    if (analysesError) {
      console.error('❌ Error fetching private analyses:', analysesError)
      return NextResponse.json(
        { error: 'Failed to fetch private analyses' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      analyses: analyses || [],
      total: analyses?.length || 0
    })

  } catch (error) {
    console.error('❌ Error in private analyses fetch:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}