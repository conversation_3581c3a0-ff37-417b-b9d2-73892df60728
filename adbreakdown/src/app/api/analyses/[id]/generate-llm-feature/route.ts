import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params
    const { id } = await params
    
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get request body
    const body = await req.json()
    const { report_type_name } = body

    if (!report_type_name) {
      return NextResponse.json(
        { error: 'Report type is required' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get user profile to check credits
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('credits_remaining, subscription_status')
      .eq('user_id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      )
    }

    // Get report type information
    const { data: reportType, error: reportTypeError } = await supabase
      .from('report_types')
      .select('id, credit_cost')
      .eq('name', report_type_name)
      .single()

    if (reportTypeError || !reportType) {
      return NextResponse.json(
        { error: 'Invalid report type' },
        { status: 400 }
      )
    }

    // Credit check disabled for testing
    console.log(`🧪 Testing mode: Bypassing credit check for ${report_type_name}`)
    // if (profile.credits_remaining < reportType.credit_cost) {
    //   return NextResponse.json(
    //     { 
    //       error: 'Insufficient credits',
    //       credits_required: reportType.credit_cost,
    //       credits_remaining: profile.credits_remaining
    //     },
    //     { status: 402 }
    //   )
    // }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // Verify analysis ownership
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('id, status')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)
      .single()

    if (analysisError || !analysis) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    // Check if analysis is completed
    if (analysis.status !== 'completed') {
      return NextResponse.json(
        { error: 'Analysis must be completed before generating additional features' },
        { status: 400 }
      )
    }

    // Check if this report already exists (use actual UUID)
    const { data: existingReport, error: existingReportError } = await supabase
      .from('analysis_reports')
      .select('id, status')
      .eq('analysis_id', analysis.id)
      .eq('report_type_id', reportType.id)
      .single()

    if (existingReport && existingReport.status === 'generated') {
      return NextResponse.json(
        { 
          message: 'Feature already generated',
          report_id: existingReport.id
        },
        { status: 200 }
      )
    }

    // Create or update analysis report
    let reportId: string
    if (existingReport) {
      // Update existing report to regenerating status
      const { data: updatedReport, error: updateError } = await supabase
        .from('analysis_reports')
        .update({ status: 'generating' })
        .eq('id', existingReport.id)
        .select('id')
        .single()
      
      if (updateError) {
        console.error('Error updating report:', updateError)
        return NextResponse.json(
          { error: 'Failed to update report' },
          { status: 500 }
        )
      }
      reportId = updatedReport.id
    } else {
      // Create new analysis report (use actual UUID)
      const { data: newReport, error: createError } = await supabase
        .from('analysis_reports')
        .insert({
          analysis_id: analysis.id,
          report_type_id: reportType.id,
          status: 'generating'
        })
        .select('id')
        .single()

      if (createError) {
        console.error('Error creating report:', createError)
        return NextResponse.json(
          { error: 'Failed to create report' },
          { status: 500 }
        )
      }
      reportId = newReport.id
    }

    // Generate content directly using Node.js and Gemini API
    let finalGeneratedContent = ''
    try {
      console.log(`🚀 Generating ${report_type_name} content directly via Node.js`)
      
      // Get analysis data for context
      const { data: analysisData, error: analysisDataError } = await supabase
        .from('ad_analyses')
        .select('*')
        .eq('id', analysis.id)
        .single()
      
      if (analysisDataError || !analysisData) {
        throw new Error('Failed to fetch analysis data for content generation')
      }
      
      // Import prompt functions
      const { getMarketingCopyPrompt } = await import('@/lib/prompts/marketingCopyPrompt')
      const { getSocialMediaPostsPrompt } = await import('@/lib/prompts/socialMediaPostsPrompt')
      const { getMarketingScorecardPrompt } = await import('@/lib/prompts/marketingScorecardPrompt')
      const { getSeoKeywordsPrompt } = await import('@/lib/prompts/seoKeywordsPrompt')
      const { getContentSuggestionsPrompt } = await import('@/lib/prompts/contentSuggestionsPrompt')
      
      let prompt = ''
      const videoInfo = `${analysisData.transcript || ''}\n\n${analysisData.summary || ''}`
      const marketingAnalysis = analysisData.marketing_analysis || ''
      
      // Generate appropriate prompt based on feature type
      switch (report_type_name) {
        case 'marketing_copy':
          prompt = getMarketingCopyPrompt(videoInfo, marketingAnalysis)
          break
        case 'social_media_posts':
          prompt = getSocialMediaPostsPrompt(videoInfo, marketingAnalysis)
          break
        case 'marketing_scorecard':
          prompt = getMarketingScorecardPrompt(marketingAnalysis)
          break
        case 'seo_keywords':
          prompt = getSeoKeywordsPrompt(videoInfo)
          break
        case 'content_suggestions':
          prompt = getContentSuggestionsPrompt(marketingAnalysis)
          break
        default:
          throw new Error(`Unsupported report type: ${report_type_name}`)
      }
      
      // Generate content using direct Gemini API call
      console.log(`📝 Generating content with Gemini for ${report_type_name}`)
      
      const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY
      if (!apiKey) {
        throw new Error('Gemini API key not found')
      }
      
      const payload = {
        contents: [{
          role: "user",
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: 0.8,
          topP: 0.95,
          maxOutputTokens: 4096,
        }
      }
      
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key=${apiKey}`
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`Gemini API Error: ${errorData.error?.message || response.statusText}`)
      }
      
      const result = await response.json()
      
      if (!result.candidates || result.candidates.length === 0 ||
          !result.candidates[0].content || !result.candidates[0].content.parts ||
          result.candidates[0].content.parts.length === 0) {
        throw new Error("Could not get valid response from Gemini.")
      }
      
      const generatedContent = result.candidates[0].content.parts[0].text
      
      // Update the report with generated content
      const { error: updateError } = await supabase
        .from('analysis_reports')
        .update({
          content: { raw_content: generatedContent },
          status: 'generated',
          generated_at: new Date().toISOString()
        })
        .eq('id', reportId)
      
      if (updateError) {
        console.error('Error updating report:', updateError)
        throw new Error('Failed to save generated content')
      }
      
      console.log(`✅ Successfully generated ${report_type_name} content`)
      
      // Store the generated content for the response
      finalGeneratedContent = generatedContent
      
    } catch (generationError) {
      console.error('Error generating content:', generationError)
      
      // Mark report as error
      await supabase
        .from('analysis_reports')
        .update({ status: 'error' })
        .eq('id', reportId)
        
      return NextResponse.json(
        { error: `Failed to generate ${report_type_name}: ${generationError.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { 
        message: 'Feature generated successfully',
        report_id: reportId,
        analysis_id: analysis.id,
        generated_content: finalGeneratedContent,
        credits_used: reportType.credit_cost
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error in generate-llm-feature:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
