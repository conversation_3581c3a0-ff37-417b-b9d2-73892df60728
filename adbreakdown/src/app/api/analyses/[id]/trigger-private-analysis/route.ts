import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { PRE_LAUNCH_ANALYSIS_PROMPT } from '@/lib/prompts/preLaunchAnalysisPrompt'
import { VertexAI } from '@google-cloud/vertexai'

interface RouteParams {
  params: Promise<{ 
    id: string
  }>
}

// POST /api/analyses/[id]/trigger-private-analysis - Trigger private analysis processing
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get the private analysis
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .eq('is_private', true)
      .single()

    if (analysisError || !analysis) {
      console.error('Private analysis not found:', { id, userId: user.id, error: analysisError })
      return NextResponse.json(
        { error: 'Private analysis not found or access denied' },
        { status: 404 }
      )
    }

    console.log('🚀 Triggering private analysis for:', { id: analysis.id, title: analysis.title })

    // Update status to processing
    await supabase
      .from('ad_analyses')
      .update({ status: 'processing' })
      .eq('id', id)

    // Initialize Vertex AI with authentication (copied from working trigger-vertex-analysis route)
    const projectId = process.env.GOOGLE_PROJECT_ID
    const serviceAccountKey = process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY
    
    console.log('🔧 Google Cloud Authentication Check (Trigger Private):', {
      hasProjectId: !!projectId,
      hasServiceAccountKey: !!serviceAccountKey,
      serviceAccountKeyLength: serviceAccountKey?.length || 0,
      nodeEnv: process.env.NODE_ENV
    })
    
    if (!projectId) {
      console.error('❌ Missing GOOGLE_PROJECT_ID environment variable')
      return NextResponse.json(
        { error: 'Google Cloud Project ID not configured' },
        { status: 500 }
      )
    }

    let vertex_ai: VertexAI
    
    // Check if we have service account key for production
    if (serviceAccountKey) {
      try {
        console.log('🔑 Attempting to parse service account key...')
        let credentials;

        // The service account key might be a raw JSON string or a Base64 encoded string.
        // We'll try parsing it as raw JSON first.
        try {
          credentials = JSON.parse(serviceAccountKey);
          console.log('✅ Successfully parsed raw JSON from service account key.');
        } catch (rawParseError) {
          // If raw parsing fails, assume it's Base64 encoded, which is a common and robust way to store JSON in env vars.
          console.log('⚠️ Raw JSON parse failed, attempting Base64 decoding...');
          const decodedKey = Buffer.from(serviceAccountKey, 'base64').toString('utf-8');
          credentials = JSON.parse(decodedKey);
          console.log('✅ Successfully parsed Base64 decoded service account key.');
        }
        
        vertex_ai = new VertexAI({
          project: projectId,
          location: 'us-central1',
          googleAuthOptions: {
            credentials
          }
        })
        console.log('🔐 Successfully initialized Vertex AI with service account key authentication')
      } catch (error: any) {
        console.error('❌ Failed to parse service account key (tried raw and Base64):', {
          parseError: error.message,
          keyPreview: serviceAccountKey.substring(0, 100) + '...'
        })
        return NextResponse.json(
          { error: `Invalid service account credentials: ${error.message}` },
          { status: 500 }
        )
      }
    } else {
      console.log('⚠️ No service account key found, using default authentication (this will fail in production)')
      // Use default authentication (works in dev with gcloud auth)
      vertex_ai = new VertexAI({
        project: projectId,
        location: 'us-central1',
      })
      console.log('🔐 Initialized Vertex AI with default authentication (dev only)')
    }

    // Enhanced generation config (matching working route)
    const generationConfig = {
      temperature: 0.9,
      topP: 0.95,
      maxOutputTokens: 65535,
    };

    // Safety settings (matching working route - all off)
    const safetySettings = [
      {
        category: 'HARM_CATEGORY_HATE_SPEECH' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT' as any, 
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT' as any,
        threshold: 'BLOCK_NONE' as any
      }
    ];

    const model = vertex_ai.getGenerativeModel({ 
      model: 'gemini-2.5-pro',
      generationConfig,
      safetySettings
    })

    console.log('📝 Using PRE_LAUNCH_ANALYSIS_PROMPT for private analysis trigger')
    const promptContent = PRE_LAUNCH_ANALYSIS_PROMPT
    
    console.log('📊 PRIVATE ANALYSIS TRIGGER CONFIG:', {
      model: 'gemini-2.5-pro',
      temperature: 0.9,
      topP: 0.95,
      maxTokens: 65535,
      safetySettings: 'All disabled',
      promptLength: promptContent.length,
      analysisMethod: 'Vertex AI Private Analysis Trigger'
    })
    
    const systemInstruction = {
      role: "system",
      parts: [{ text: promptContent }],
    };

    const videoPart = {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: analysis.youtube_url,
      },
    };

    // Vertex AI requires a text part alongside a file part in multimodal requests.
    const textPart = {
      text: `Please analyze the provided video advertisement using the pre-launch analysis framework. Focus on providing actionable recommendations for optimizing this ad before it launches publicly.

**Video Information:**
- Title: ${analysis.title || 'Unknown Title'}
- Brand/Channel: ${analysis.inferred_brand || 'Unknown Brand'}
- Duration: ${analysis.duration_seconds || 0} seconds
- YouTube URL: ${analysis.youtube_url}
- Video ID: ${analysis.youtube_video_id}`
    };

    console.log('🤖 Sending request to Vertex AI for private analysis')

    // Generate the analysis using proper multimodal request format
    const result = await model.generateContent({
      contents: [{ role: "user", parts: [textPart, videoPart] }],
      systemInstruction
      // generationConfig and safetySettings are already configured in the model
    });

    const response = result.response;

    // Extract text response (matching working route pattern)
    if (!response.candidates?.length || !response.candidates[0].content?.parts[0]?.text) {
      console.error("❌ Invalid response structure from Vertex AI:", JSON.stringify(response, null, 2));
      throw new Error("Received an invalid or empty text response from Vertex AI.");
    }

    const analysisText = response.candidates[0].content.parts[0].text;

    console.log('✅ Received analysis from Vertex AI, length:', analysisText.length)

    // Update the analysis record with results
    const { error: updateError } = await supabase
      .from('ad_analyses')
      .update({
        marketing_analysis: analysisText,
        status: 'completed',
        analysis_completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)

    if (updateError) {
      console.error('❌ Error updating private analysis:', updateError)
      
      // Mark as failed
      await supabase
        .from('ad_analyses')
        .update({ 
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
      
      return NextResponse.json(
        { error: 'Failed to save analysis results' },
        { status: 500 }
      )
    }

    console.log('✅ Private analysis completed successfully:', id)

    return NextResponse.json({
      success: true,
      message: 'Private analysis completed successfully',
      analysis: {
        id: analysis.id,
        status: 'completed'
      }
    })

  } catch (error) {
    console.error('❌ Error in private analysis trigger:', error)
    
    // Try to mark the analysis as failed
    try {
      const { id } = await params
      const supabase = createServerSupabaseClient()
      await supabase
        .from('ad_analyses')
        .update({ 
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
    } catch (markError) {
      console.error('❌ Error marking analysis as failed:', markError)
    }

    return NextResponse.json(
      { error: 'Internal server error during analysis processing' },
      { status: 500 }
    )
  }
}