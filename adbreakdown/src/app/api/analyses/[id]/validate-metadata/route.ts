import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { getMetadataValidationPrompt } from '@/lib/prompts/metadataValidationPrompt'

interface RouteParams {
  params: Promise<{ 
    id: string
  }>
}

// POST /api/analyses/{id}/validate-metadata
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    console.log('🔍 METADATA VALIDATION: Starting validation for analysis', id)

    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create server Supabase client
    const supabase = createServerSupabaseClient()
    
    // ✅ CRITICAL: UUID/Slug resolution pattern to prevent database errors
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    console.log('🔧 ID resolution:', { id, isUUID, fieldToMatch: isUUID ? 'id' : 'slug' })
    
    // Get user from database
    console.log('🔍 Looking for user with Clerk ID:', userId)
    
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      console.error('❌ User not found or error:', userError)
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get the analysis with marketing data
    console.log('🔍 Looking for analysis with:', { 
      searchBy: isUUID ? 'id' : 'slug', 
      searchValue: id, 
      userId: user.id 
    })
    
    // First check if any records exist with this id/slug (for debugging)
    const { data: allMatches, error: debugError } = await supabase
      .from('ad_analyses')
      .select('id, user_id, slug')
      .eq(isUUID ? 'id' : 'slug', id)
    
    console.log('🔍 Debug - All matching records:', allMatches?.length || 0, allMatches)
    
    if (allMatches && allMatches.length > 0) {
      const record = allMatches[0]
      console.log('🔍 Ownership check:', {
        currentUserId: user.id,
        recordUserId: record.user_id,
        match: user.id === record.user_id
      })
      
      if (user.id !== record.user_id) {
        console.error('❌ User ownership mismatch - current user does not own this analysis')
        return NextResponse.json(
          { error: 'Analysis not found or access denied: User ownership mismatch' },
          { status: 403 }
        )
      }
    }
    
    const { data: analysis, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('id, user_id, slug, youtube_url, marketing_analysis')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)  // ← Verify ownership
      .single()

    if (fetchError || !analysis) {
      console.error('❌ Failed to fetch analysis for validation:', fetchError)
      return NextResponse.json(
        { error: `Analysis not found or access denied: ${fetchError?.message || 'Record not found'}` },
        { status: 404 }
      )
    }

    if (!analysis.marketing_analysis) {
      return NextResponse.json(
        { error: 'No marketing analysis found to validate. Please run the initial analysis first.' },
        { status: 400 }
      )
    }

    console.log('✅ Found analysis for validation:', { analysisId: analysis.id, slug: analysis.slug })

    // Parse the current marketing analysis
    let currentMetadata;
    try {
      const marketingAnalysis = typeof analysis.marketing_analysis === 'string' 
        ? JSON.parse(analysis.marketing_analysis) 
        : analysis.marketing_analysis;
      
      currentMetadata = marketingAnalysis.metadata;
      
      if (!currentMetadata) {
        return NextResponse.json(
          { error: 'No metadata found in marketing analysis' },
          { status: 400 }
        )
      }
    } catch (parseError) {
      console.error('❌ Failed to parse marketing analysis:', parseError)
      return NextResponse.json(
        { error: 'Invalid marketing analysis format' },
        { status: 400 }
      )
    }

    console.log('🔍 Current metadata to validate:', currentMetadata)

    // Generate the validation prompt
    const validationPrompt = getMetadataValidationPrompt(analysis.youtube_url, currentMetadata)
    
    console.log('🤖 Calling Gemini API for metadata validation...')
    console.log('🔍 Metadata to validate:', JSON.stringify(currentMetadata, null, 2))

    // Call Gemini API with search grounding
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      )
    }

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key=${apiKey}`
    
    // Try multiple approaches - first with search grounding, then fallback
    let result;
    let searchUsed = false;

    // Approach 1: Try with Google Search grounding (without JSON mode)
    try {
      console.log('🔍 Attempting validation with Google Search grounding...')
      
      const payloadWithSearch = {
        contents: [
          {
            role: "user",
            parts: [{ text: validationPrompt }]
          }
        ],
        tools: [
          {
            googleSearchRetrieval: {
              dynamicRetrievalConfig: {
                mode: 'MODE_DYNAMIC',
                dynamicThreshold: 0.6 // Lower threshold for more search results
              }
            }
          }
        ],
        generationConfig: {
          // NOTE: Cannot use responseMimeType with search grounding
          temperature: 0.2, // Very low temperature for factual responses
          topP: 0.8,
        }
      }
      
      console.log('📡 Making API call with search grounding...')
      console.log('🔧 Search grounding enabled - JSON mode disabled')

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payloadWithSearch)
      })

      console.log('📡 Response status:', response.status, response.statusText)

      if (response.ok) {
        result = await response.json()
        searchUsed = true
        console.log('✅ Search grounding successful')
        console.log('📄 Response structure:', Object.keys(result))
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Failed to parse error' }))
        console.warn('⚠️ Search grounding failed:', errorData)
        throw new Error(`Search grounding failed: ${response.status}`)
      }
    } catch (searchError) {
      console.warn('⚠️ Google Search grounding failed, trying fallback approach:', searchError)
      
      // Approach 2: Fallback without search grounding but with JSON mode
      const fallbackPrompt = `You are an expert marketing analyst. Validate and enhance this metadata for a YouTube advertisement.

IMPORTANT: Even without direct web access, use your knowledge to:
1. Check spelling and formatting of brand names
2. Verify common celebrity endorsements and partnerships
3. Suggest improvements based on industry standards
4. Provide confidence scores based on your training data

YouTube URL: ${analysis.youtube_url}
Current Metadata: ${JSON.stringify(currentMetadata, null, 2)}

${validationPrompt}`

      const payloadFallback = {
        contents: [
          {
            role: "user",
            parts: [{ text: fallbackPrompt }]
          }
        ],
        generationConfig: {
          temperature: 0.2,
          topP: 0.8,
        }
      }

      console.log('🔄 Trying fallback approach with JSON mode (no search grounding)...')
      
      const fallbackResponse = await fetch(apiUrl, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payloadFallback)
      })

      if (!fallbackResponse.ok) {
        const errorData = await fallbackResponse.json().catch(() => ({ error: 'Failed to parse error' }))
        console.error('❌ Both approaches failed:', errorData)
        return NextResponse.json(
          { error: `AI validation failed: ${errorData.error?.message || fallbackResponse.statusText}` },
          { status: 500 }
        )
      }

      result = await fallbackResponse.json()
      searchUsed = false
      console.log('✅ Fallback approach successful (JSON mode enabled, no search grounding)')
    }
    
    if (!result.candidates?.[0]?.content?.parts?.[0]?.text) {
      console.error('❌ Invalid response structure from Gemini:', result)
      return NextResponse.json(
        { error: 'Invalid response from AI validation service' },
        { status: 500 }
      )
    }

    const validationText = result.candidates[0].content.parts[0].text
    
    // Extract grounding metadata (source URLs) if search grounding was used
    console.log('🔍 Extracting grounding metadata...')
    const groundingMetadata = result.candidates?.[0]?.groundingMetadata
    let actualSourceUrls = []
    
    if (groundingMetadata && searchUsed) {
      console.log('📚 Processing grounding metadata:', JSON.stringify(groundingMetadata, null, 2))
      
      // Extract URLs from grounding chunks
      if (groundingMetadata.groundingChunks) {
        console.log(`🔗 Found ${groundingMetadata.groundingChunks.length} grounding chunks`)
        for (const chunk of groundingMetadata.groundingChunks) {
          console.log('🔍 Processing chunk:', JSON.stringify(chunk, null, 2))
          if (chunk.web && chunk.web.uri) {
            console.log(`✅ Adding source URL: ${chunk.web.uri}`)
            actualSourceUrls.push({
              url: chunk.web.uri,
              title: chunk.web.title || 'Web Source',
              reliability_score: 8, // High reliability for grounded sources
              source_type: 'grounding_source'
            })
          }
        }
      } else {
        console.log('⚠️ No groundingChunks found in metadata')
      }
      
      // Also check web searches if available
      if (groundingMetadata.webSearchQueries) {
        console.log('🔍 Web search queries found:', groundingMetadata.webSearchQueries)
      }
      
      console.log(`🔗 Found ${actualSourceUrls.length} actual source URLs from grounding`)
    } else {
      console.log('📚 No grounding metadata available (search not used or no metadata returned)')
    }

    // Parse the validation result
    let validationResult;
    try {
      // Clean the response (similar to other API routes)
      let cleanValidationText = validationText
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .trim()
      
      const firstBrace = cleanValidationText.indexOf('{')
      const lastBrace = cleanValidationText.lastIndexOf('}')
      
      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        cleanValidationText = cleanValidationText.substring(firstBrace, lastBrace + 1)
      }
      
      validationResult = JSON.parse(cleanValidationText)
    } catch (parseError) {
      console.error('❌ Failed to parse validation result:', parseError)
      console.error('Raw validation text:', validationText)
      return NextResponse.json(
        { error: 'Failed to parse AI validation result' },
        { status: 500 }
      )
    }

    console.log('✅ Validation completed successfully')
    console.log('🔍 Search grounding used:', searchUsed)

    // Save the validation result to database
    console.log('💾 Saving validation result to database...')
    const { error: saveError } = await supabase
      .from('ad_analyses')
      .update({
        llm_correction: {
          type: 'metadata_validation',
          timestamp: new Date().toISOString(),
          search_grounding_used: searchUsed,
          validation_result: validationResult,
          original_metadata: currentMetadata
        }
      })
      .eq('id', analysis.id)

    if (saveError) {
      console.warn('⚠️ Failed to save validation result to database:', saveError)
      // Don't fail the request, just log the warning
    } else {
      console.log('✅ Validation result saved to database')
    }

    return NextResponse.json({
      message: 'Metadata validation completed successfully',
      analysis_id: analysis.id,
      slug: analysis.slug,
      validation_result: validationResult,
      original_metadata: currentMetadata,
      search_grounding_used: searchUsed,
      saved_to_database: !saveError
    }, { status: 200 })

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    console.error('❌ Error in validate-metadata:', errorMessage)
    
    return NextResponse.json(
      { error: `Metadata validation failed: ${errorMessage}` },
      { status: 500 }
    )
  }
}