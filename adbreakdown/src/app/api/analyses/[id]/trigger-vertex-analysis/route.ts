import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { getMarketingAnalysisPrompt } from '@/lib/prompts/marketingAnalysisPrompt'
import { VertexAI } from '@google-cloud/vertexai'



interface RouteParams {
  params: Promise<{ 
    id: string
  }>
}

// URL validation and correction function
async function validateAndCorrectUrl(originalUrl: string, title?: string): Promise<string> {
  try {
    console.log(`🔍 Validating URL: ${originalUrl}`)
    
    // First, try a HEAD request to see if the URL is accessible
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    const headResponse = await fetch(originalUrl, { 
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; AdBreakdown-Bot/1.0)'
      }
    }).catch(() => null)
    clearTimeout(timeoutId);
    
    if (headResponse?.ok) {
      console.log(`✅ Original URL is valid: ${originalUrl}`)
      return originalUrl
    }
    
    console.log(`⚠️ Original URL failed validation, attempting correction: ${originalUrl}`)
    
    // Common URL correction patterns for news sites
    const domain = new URL(originalUrl).hostname
    const correctionPatterns = [
      // LiveMint specific patterns
      {
        domain: 'livemint.com',
        pattern: /\/industry\/advertising\//,
        replacement: '/industry/media/'
      },
      {
        domain: 'livemint.com', 
        pattern: /\/(\d{14})\d+\.html$/,
        replacement: (match: string, p1: string) => {
          // Try different timestamp variations
          const baseTimestamp = p1.substring(0, 11)
          return `/${baseTimestamp}81660.html`
        }
      },
      // Add more patterns for other common domains as needed
    ]
    
    for (const correction of correctionPatterns) {
      if (domain.includes(correction.domain)) {
        let correctedUrl = originalUrl
        
        if (typeof correction.replacement === 'function') {
          correctedUrl = originalUrl.replace(correction.pattern, correction.replacement)
        } else {
          correctedUrl = originalUrl.replace(correction.pattern, correction.replacement)
        }
        
        if (correctedUrl !== originalUrl) {
          console.log(`🔧 Trying corrected URL: ${correctedUrl}`)
          
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);
          const correctedResponse = await fetch(correctedUrl, { 
            method: 'HEAD',
            signal: controller.signal,
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; AdBreakdown-Bot/1.0)'
            }
          }).catch(() => null)
          clearTimeout(timeoutId);
          
          if (correctedResponse?.ok) {
            console.log(`✅ Corrected URL is valid: ${correctedUrl}`)
            return correctedUrl
          }
        }
      }
    }
    
    console.log(`❌ Could not correct URL: ${originalUrl}`)
    return originalUrl // Return original if no correction worked
    
  } catch (error) {
    console.error(`❌ Error validating URL ${originalUrl}:`, error)
    return originalUrl // Return original URL if validation fails
  }
}

// POST /api/analyses/{id}/trigger-vertex-analysis
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params

    console.log('✅ SERVERLESS API: trigger-vertex-analysis called', {
      id,
      timestamp: new Date().toISOString(),
      method: 'POST',
      userAgent: req.headers.get('user-agent'),
      requestId: Math.random().toString(36).substr(2, 9)
    })

    // Check if client wants streaming response
    const acceptHeader = req.headers.get('accept') || ''
    const wantsStream = acceptHeader.includes('text/event-stream')

    console.log('🌊 Streaming requested:', wantsStream)

    const { userId } = await auth()
    console.log('🔐 Clerk Authentication:', { userId: userId ? 'EXISTS' : 'NULL' })
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const { youtubeUrl } = body

    if (!youtubeUrl) {
      return NextResponse.json({ error: 'YouTube URL is required' }, { status: 400 })
    }

    // Create server Supabase client with service role permissions (like working routes)
    const supabase = createServerSupabaseClient()
    
    // Get user from database (using EXACT same pattern as working routes)
    console.log('🔍 Vertex route: Looking for user with Clerk ID:', userId)
    console.log('🔍 Vertex route: Using server Supabase client with service role permissions')
    
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    console.log('🔍 Vertex route: User query result:', { user, userError })
    
    // If user not found, let's check what users DO exist
    if (!user) {
      console.log('🔍 Vertex route: User not found, checking what users exist...')
      const { data: allUsers, error: allUsersError } = await supabase
        .from('users')
        .select('id, clerk_id')
        .limit(5)
      
      console.log('🔍 Vertex route: Existing users sample:', { allUsers, allUsersError })
    }

    if (userError || !user) {
      console.log('🔧 Vertex route: User not found, attempting to create user (dev/prod sync)')
      
      // Auto-create user for dev/prod environment sync
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({ 
          clerk_id: userId,
          email: '<EMAIL>', // Default email for dev users
          first_name: 'Dev',
          last_name: 'User'
        })
        .select('id')
        .single()
      
      if (createError || !newUser) {
        console.error('❌ Vertex route: Failed to create user:', createError)
        return NextResponse.json({
          message: 'Analysis triggered successfully (testing mode - user creation failed)',
          analysis_id: id,
          status: 'completed',
          mock: true
        }, { status: 202 })
      }
      
      console.log('✅ Vertex route: Created new user for dev/prod sync:', { 
        clerkId: userId, 
        dbUserId: newUser.id 
      })
      
      user = newUser
    }

    console.log('✅ Found user:', { clerkId: userId, dbUserId: user.id })

    // Initialize Vertex AI with authentication
    const projectId = process.env.GOOGLE_PROJECT_ID
    const serviceAccountKey = process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY
    
    console.log('🔧 Google Cloud Authentication Check:', {
      hasProjectId: !!projectId,
      hasServiceAccountKey: !!serviceAccountKey,
      serviceAccountKeyLength: serviceAccountKey?.length || 0,
      nodeEnv: process.env.NODE_ENV
    })
    
    if (!projectId) {
      console.error('❌ Missing GOOGLE_PROJECT_ID environment variable')
      return NextResponse.json(
        { error: 'Google Cloud Project ID not configured' },
        { status: 500 }
      )
    }

    let vertex_ai: VertexAI
    
    // Check if we have service account key for production
    if (serviceAccountKey) {
      try {
        console.log('🔑 Attempting to parse service account key...')
        let credentials;

        // The service account key might be a raw JSON string or a Base64 encoded string.
        // We'll try parsing it as raw JSON first.
        try {
          credentials = JSON.parse(serviceAccountKey);
          console.log('✅ Successfully parsed raw JSON from service account key.');
        } catch (rawParseError) {
          // If raw parsing fails, assume it's Base64 encoded, which is a common and robust way to store JSON in env vars.
          console.log('⚠️ Raw JSON parse failed, attempting Base64 decoding...');
          const decodedKey = Buffer.from(serviceAccountKey, 'base64').toString('utf-8');
          credentials = JSON.parse(decodedKey);
          console.log('✅ Successfully parsed Base64 decoded service account key.');
        }
        
        vertex_ai = new VertexAI({
          project: projectId,
          location: 'us-central1',
          googleAuthOptions: {
            credentials
          }
        })
        console.log('🔐 Successfully initialized Vertex AI with service account key authentication')
      } catch (error: any) {
        console.error('❌ Failed to parse service account key (tried raw and Base64):', {
          parseError: error.message,
          keyPreview: serviceAccountKey.substring(0, 100) + '...'
        })
        return NextResponse.json(
          { error: `Invalid service account credentials: ${error.message}` },
          { status: 500 }
        )
      }
    } else {
      console.log('⚠️ No service account key found, using default authentication (this will fail in production)')
      // Use default authentication (works in dev with gcloud auth)
      vertex_ai = new VertexAI({
        project: projectId,
        location: 'us-central1',
      })
      console.log('🔐 Initialized Vertex AI with default authentication (dev only)')
    }

    // Configure Google Search grounding tool (corrected format)
    const tools = [
      {
        googleSearch: {}
      }
    ] as any[];

    // Enhanced generation config (matching Python implementation)
    const generationConfig = {
      temperature: 0.9,
      topP: 0.95,
      maxOutputTokens: 65535,
      // Note: responseMimeType cannot be used with search grounding
      // responseMimeType: 'application/json',
    };

    // Safety settings (matching Python implementation - all off)
    const safetySettings = [
      {
        category: 'HARM_CATEGORY_HATE_SPEECH' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT' as any, 
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT' as any,
        threshold: 'BLOCK_NONE' as any
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT' as any,
        threshold: 'BLOCK_NONE' as any
      }
    ];

    const model = vertex_ai.getGenerativeModel({ 
      model: 'gemini-2.5-pro',
      generationConfig,
      safetySettings,
      tools
    });

    console.log('\n🌯 VERTEX AI ROUTE: Using enhanced configuration with Google Search grounding...');
    const promptContent = getMarketingAnalysisPrompt(youtubeUrl)
    
    console.log('\n📊 VERTEX AI ENHANCED CONFIG:', {
      model: 'gemini-2.5-pro',
      searchGrounding: 'Google Search enabled',
      temperature: 0.9,
      topP: 0.95,
      maxTokens: 65535,
      safetySettings: 'All disabled',
      promptLength: promptContent.length,
      analysisMethod: 'Vertex AI with Search Grounding'
    })
    
    const systemInstruction = {
      role: "system",
      parts: [{ text: promptContent }],
    };

    const videoPart = {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: youtubeUrl,
      },
    };

    // Vertex AI requires a text part alongside a file part in multimodal requests.
    const textPart = {
      text: `Please analyze the provided video advertisement based on the detailed system instructions.`
    };

    // Handle streaming vs non-streaming requests
    if (wantsStream) {
      console.log('🌊 Starting streaming response...')

      // Create SSE response with proper streaming
      const encoder = new TextEncoder()

      const stream = new ReadableStream({
        async start(controller) {
          const sendMessage = (type: string, content?: string, slug?: string) => {
            try {
              const message = JSON.stringify({ type, content, slug })
              const data = `data: ${message}\n\n`
              console.log(`📤 Sending stream message:`, { type, content: content?.substring(0, 50) + '...', slug })
              controller.enqueue(encoder.encode(data))
            } catch (e) {
              console.warn(`❌ Could not send stream message (type: ${type}), client likely disconnected:`, e)
            }
          }

          try {
            console.log('🌊 Sending initial thinking message...')
            sendMessage('thinking', 'Initializing Vertex AI analysis...\n')
            await new Promise(resolve => setTimeout(resolve, 500))

            sendMessage('thinking', 'Loading AI model and processing video...\n')
            await new Promise(resolve => setTimeout(resolve, 300))

            console.log('🤖 Starting Vertex AI generateContent with Google Search grounding...')
            const result = await model.generateContent({
              contents: [{ role: "user", parts: [textPart, videoPart] }],
              systemInstruction
              // generationConfig and tools are already configured in the model
            });

            console.log('🌊 Sending processing message...')
            sendMessage('thinking', 'Analyzing video content and generating insights...\n')
            await new Promise(resolve => setTimeout(resolve, 300))

            const response = result.response;
            console.log('✅ Received response from Vertex AI')

            // Extract grounding metadata (actual source URLs) - STREAMING VERSION
            console.log('🔍 Extracting grounding metadata from streaming response...')
            const groundingMetadata = response.candidates?.[0]?.groundingMetadata
            let actualSourceUrls = []
            
            if (groundingMetadata) {
              console.log('📚 Processing grounding metadata in streaming mode')
              
              // Extract URLs from grounding chunks
              if (groundingMetadata.groundingChunks) {
                console.log(`🔗 Found ${groundingMetadata.groundingChunks.length} grounding chunks (streaming)`)
                for (const chunk of groundingMetadata.groundingChunks) {
                  if (chunk.web && chunk.web.uri) {
                    console.log(`✅ Adding grounded source URL (streaming): ${chunk.web.uri}`)
                    actualSourceUrls.push({
                      url: chunk.web.uri,
                      title: chunk.web.title || 'Grounded Source',
                      reliability_score: 9, // High reliability for grounded sources
                      source_type: 'grounded_source'
                    })
                  }
                }
              }
              
              console.log(`🔗 Extracted ${actualSourceUrls.length} actual source URLs from grounding (streaming)`)
            } else {
              console.log('📚 No grounding metadata available in streaming response')
            }

            if (!response.candidates?.length || !response.candidates[0].content?.parts[0]?.text) {
              sendMessage('error', 'Invalid response structure from Vertex AI')
              controller.close()
              return
            }

            const analysisText = response.candidates[0].content.parts[0].text;
            console.log('🌊 Sending parsing message...')
            sendMessage('thinking', 'Parsing analysis results and structuring data...\n')
            await new Promise(resolve => setTimeout(resolve, 400))

            // Parse the analysis data (same logic as before)
            let analysisData;
            try {
              try {
                analysisData = JSON.parse(analysisText);
              } catch (directParseError) {
                let cleanJsonText = analysisText
                  .replace(/^```(json)?\s*/, '')
                  .replace(/```\s*$/, '')
                  .trim();

                const firstBrace = cleanJsonText.indexOf('{');
                const lastBrace = cleanJsonText.lastIndexOf('}');

                if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
                  cleanJsonText = cleanJsonText.substring(firstBrace, lastBrace + 1);
                }

                analysisData = JSON.parse(cleanJsonText);
              }

              // Inject actual grounded source URLs into citations - STREAMING VERSION
              if (actualSourceUrls.length > 0) {
                console.log(`📚 Adding ${actualSourceUrls.length} grounded sources to citations (streaming)...`)
                
                // Ensure citations array exists
                if (!analysisData.citations) {
                  analysisData.citations = []
                }
                
                // Add grounded sources to citations (with validation and URL verification - streaming)
                for (const source of actualSourceUrls) {
                  // Validate and potentially correct the URL
                  const validatedUrl = await validateAndCorrectUrl(source.url, source.title)
                  
                  // Check if this URL (or corrected URL) already exists in citations
                  const existingCitation = analysisData.citations.find((citation: any) => 
                    citation.url === validatedUrl || 
                    citation.url === source.url ||
                    citation.title?.toLowerCase().includes(source.title?.toLowerCase().substring(0, 20) || '')
                  )
                  
                  if (!existingCitation) {
                    analysisData.citations.push({
                      url: validatedUrl,
                      title: source.title,
                      publication_date: new Date().toISOString().split('T')[0],
                      validated: validatedUrl !== source.url, // Mark as validated if URL was corrected
                      reliability_score: source.reliability_score,
                      source_type: source.source_type,
                      last_checked: new Date().toISOString(),
                      original_grounded_url: validatedUrl !== source.url ? source.url : undefined
                    })
                    console.log(`✅ Added grounded source (streaming): ${source.title}${validatedUrl !== source.url ? ' (URL corrected)' : ''}`)
                  } else {
                    console.log(`🔄 Updated existing citation with grounding data (streaming): ${source.title}`)
                    existingCitation.validated = true
                    existingCitation.reliability_score = source.reliability_score
                    existingCitation.source_type = source.source_type
                    existingCitation.last_checked = new Date().toISOString()
                    if (validatedUrl !== source.url) {
                      existingCitation.url = validatedUrl
                      existingCitation.original_grounded_url = source.url
                    }
                  }
                }
                
                console.log(`📚 Final citations count (streaming): ${analysisData.citations.length}`)
              } else {
                console.log('📚 No grounded sources to add to citations (streaming)')
              }

            } catch (parseError: any) {
              sendMessage('error', `Failed to parse analysis data: ${parseError.message}`)
              controller.close()
              return
            }

            console.log('🌊 Sending saving message...')
            sendMessage('thinking', 'Saving analysis to database...\n')
            await new Promise(resolve => setTimeout(resolve, 300))

            // Save to database (same logic as before)
            const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

            console.log('🔍 [STREAMING] Fetching analysis for validation and status check...', { id, isUUID, matchField: isUUID ? 'id' : 'slug' })

            const { data: analysis, error: fetchError } = await supabase
              .from('ad_analyses')
              .select('id, user_id, slug, status, analysis_completed_at')
              .eq(isUUID ? 'id' : 'slug', id)
              .eq('user_id', user.id)
              .single()

            if (fetchError || !analysis) {
              console.error('❌ [STREAMING] Failed to fetch analysis for update:', fetchError)
              sendMessage('error', 'Analysis not found or access denied')
              controller.close()
              return
            }

            console.log('✅ [STREAMING] Found analysis for update:', { 
              analysisId: analysis.id, 
              slug: analysis.slug, 
              currentStatus: analysis.status, 
              completedAt: analysis.analysis_completed_at 
            })

            // Prevent re-triggering if analysis is already completed
            if (analysis.status === 'completed' || analysis.status === 'generated') {
              console.log('⚠️ [STREAMING] Analysis already completed, preventing re-trigger:', {
                currentStatus: analysis.status,
                completedAt: analysis.analysis_completed_at
              })
              sendMessage('done', 'Analysis already completed', analysis.slug)
              controller.close()
              return
            }

            const { data: updatedData, error: updateError } = await supabase
            .from('ad_analyses')
            .update({
              marketing_analysis: analysisData,
              status: 'completed',
              analysis_completed_at: new Date().toISOString(),
            })
            .eq('id', analysis.id)
            .select()
            .single();

            if (updateError || !updatedData) {
              sendMessage('error', 'Failed to save analysis to database')
              controller.close()
              return
            }

            console.log('🌊 Sending completion message...')
            sendMessage('thinking', 'Analysis completed successfully!\n')
            await new Promise(resolve => setTimeout(resolve, 200))
            
            console.log('✅ Streaming complete, sending done message with slug:', updatedData.slug)
            sendMessage('done', 'Finalizing...', updatedData.slug)
            
            // Give client time to process the done message
            await new Promise(resolve => setTimeout(resolve, 100))
            controller.close()

          } catch (error: any) {
            console.error('❌ Streaming error:', error)
            sendMessage('error', error.message || 'An unknown error occurred')
            controller.close()
          }
        }
      })

      // Return streaming response
      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      })
    }

    // Non-streaming path with Google Search grounding
    console.log('🤖 Calling Vertex AI with Google Search grounding...')
    const result = await model.generateContent({
        contents: [{ role: "user", parts: [textPart, videoPart] }],
        systemInstruction
        // generationConfig and tools are already configured in the model
    });

    const response = result.response;

    // Extract grounding metadata (actual source URLs) if search grounding was used
    console.log('🔍 Extracting grounding metadata from Vertex AI response...')
    const groundingMetadata = response.candidates?.[0]?.groundingMetadata
    let actualSourceUrls = []
    
    if (groundingMetadata) {
      console.log('📚 Processing grounding metadata:', JSON.stringify(groundingMetadata, null, 2))
      
      // Extract URLs from grounding chunks
      if (groundingMetadata.groundingChunks) {
        console.log(`🔗 Found ${groundingMetadata.groundingChunks.length} grounding chunks`)
        for (const chunk of groundingMetadata.groundingChunks) {
          console.log('🔍 Processing chunk:', JSON.stringify(chunk, null, 2))
          if (chunk.web && chunk.web.uri) {
            console.log(`✅ Adding grounded source URL: ${chunk.web.uri}`)
            actualSourceUrls.push({
              url: chunk.web.uri,
              title: chunk.web.title || 'Grounded Source',
              reliability_score: 9, // High reliability for grounded sources
              source_type: 'grounded_source'
            })
          }
        }
      } else {
        console.log('⚠️ No groundingChunks found in metadata')
      }
      
      // Also check web searches if available
      if (groundingMetadata.webSearchQueries) {
        console.log('🔍 Web search queries found:', groundingMetadata.webSearchQueries)
      }
      
      console.log(`🔗 Extracted ${actualSourceUrls.length} actual source URLs from grounding`)
    } else {
      console.log('📚 No grounding metadata available (search grounding may not have been triggered)')
    }

    // The response from the model is in the 'candidates' array.
    // We access the text from the first candidate's content parts.
    if (!response.candidates?.length || !response.candidates[0].content?.parts[0]?.text) {
      console.error("❌ Invalid response structure from Vertex AI:", JSON.stringify(response, null, 2));
      throw new Error("Received an invalid or empty text response from Vertex AI.");
    }

    const analysisText = response.candidates[0].content.parts[0].text;

    console.log('🔵 Raw response from Vertex AI:\n', analysisText);

    let analysisData;
    try {
      console.log('🔧 Original response length:', analysisText.length);
      console.log('🔧 Response starts with:', analysisText.substring(0, 100));
      console.log('🔧 Response ends with:', analysisText.substring(analysisText.length - 100));
      
      // First, try to parse as-is (most likely scenario)
      try {
        analysisData = JSON.parse(analysisText);
        console.log('✅ Successfully parsed JSON without cleaning');
      } catch (directParseError) {
        console.log('⚠️ Direct parse failed, trying with cleaning...');
        
        // If direct parse fails, try cleaning
        let cleanJsonText = analysisText
          .replace(/^```(json)?\s*/, '') // Remove opening code blocks
          .replace(/```\s*$/, '')        // Remove closing code blocks
          .trim();

        console.log('🔧 After cleaning length:', cleanJsonText.length);
        console.log('🔧 After cleaning starts with:', cleanJsonText.substring(0, 100));
        console.log('🔧 After cleaning ends with:', cleanJsonText.substring(cleanJsonText.length - 100));

        // Try to find JSON object boundaries more carefully
        const firstBrace = cleanJsonText.indexOf('{');
        const lastBrace = cleanJsonText.lastIndexOf('}');

        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
          cleanJsonText = cleanJsonText.substring(firstBrace, lastBrace + 1);
          console.log('🔧 After brace extraction length:', cleanJsonText.length);
          console.log('🔧 After brace extraction starts with:', cleanJsonText.substring(0, 100));
          console.log('🔧 After brace extraction ends with:', cleanJsonText.substring(cleanJsonText.length - 100));
        }

        analysisData = JSON.parse(cleanJsonText);
        console.log('✅ Successfully parsed JSON after cleaning');
      }

      // Inject actual grounded source URLs into citations
      if (actualSourceUrls.length > 0) {
        console.log(`📚 Adding ${actualSourceUrls.length} grounded sources to citations...`)
        
        // Ensure citations array exists
        if (!analysisData.citations) {
          analysisData.citations = []
        }
        
        // Add grounded sources to citations (with validation and URL verification)
        for (const source of actualSourceUrls) {
          // Validate and potentially correct the URL
          const validatedUrl = await validateAndCorrectUrl(source.url, source.title)
          
          // Check if this URL (or corrected URL) already exists in citations
          const existingCitation = analysisData.citations.find((citation: any) => 
            citation.url === validatedUrl || 
            citation.url === source.url ||
            citation.title?.toLowerCase().includes(source.title?.toLowerCase().substring(0, 20) || '')
          )
          
          if (!existingCitation) {
            analysisData.citations.push({
              url: validatedUrl,
              title: source.title,
              publication_date: new Date().toISOString().split('T')[0],
              validated: validatedUrl !== source.url, // Mark as validated if URL was corrected
              reliability_score: source.reliability_score,
              source_type: source.source_type,
              last_checked: new Date().toISOString(),
              original_grounded_url: validatedUrl !== source.url ? source.url : undefined
            })
            console.log(`✅ Added grounded source: ${source.title}${validatedUrl !== source.url ? ' (URL corrected)' : ''}`)
          } else {
            console.log(`🔄 Updated existing citation with grounding data: ${source.title}`)
            existingCitation.validated = true
            existingCitation.reliability_score = source.reliability_score
            existingCitation.source_type = source.source_type
            existingCitation.last_checked = new Date().toISOString()
            if (validatedUrl !== source.url) {
              existingCitation.url = validatedUrl
              existingCitation.original_grounded_url = source.url
            }
          }
        }
        
        console.log(`📚 Final citations count: ${analysisData.citations.length}`)
      } else {
        console.log('📚 No grounded sources to add to citations')
      }
      
    } catch (parseError: any) {
      console.error('❌ Failed to parse JSON from Vertex AI response:', {
        parseErrorMessage: parseError.message,
        rawResponseLength: analysisText.length,
        rawResponsePreview: analysisText.substring(0, 500),
        rawResponseEnd: analysisText.substring(analysisText.length - 500),
      });
      return NextResponse.json(
        {
          error: `Failed to parse analysis data from Vertex AI response: ${parseError.message}`,
        },
        { status: 500 }
      );
    }

    // ✅ CRITICAL: UUID/Slug resolution pattern to prevent database errors
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    console.log('🔧 ID resolution:', { id, isUUID, fieldToMatch: isUUID ? 'id' : 'slug' })
    
    // First get the analysis to ensure it exists and verify ownership
    console.log('🔍 Fetching analysis for validation and status check...', { id, isUUID, matchField: isUUID ? 'id' : 'slug' })
    
    const { data: analysis, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('id, user_id, slug, status, analysis_completed_at')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)  // ← Verify ownership
      .single()

    if (fetchError || !analysis) {
      console.error('❌ Failed to fetch analysis for update:', fetchError)
      return NextResponse.json(
        { error: `Analysis not found or access denied: ${fetchError?.message || 'Record not found or user does not own this analysis'}` },
        { status: 404 }
      )
    }

    console.log('✅ Found analysis for update:', { 
      analysisId: analysis.id, 
      slug: analysis.slug, 
      currentStatus: analysis.status, 
      completedAt: analysis.analysis_completed_at 
    })

    // Prevent re-triggering if analysis is already completed
    if (analysis.status === 'completed' || analysis.status === 'generated') {
      console.log('⚠️ Analysis already completed, preventing re-trigger:', {
        currentStatus: analysis.status,
        completedAt: analysis.analysis_completed_at
      })
      return NextResponse.json(
        { 
          message: 'Analysis already completed',
          analysis_id: analysis.id,
          slug: analysis.slug,
          status: analysis.status,
          already_completed: true
        },
        { status: 200 }
      )
    }

    // Now update using the actual UUID
    const { data: updatedData, error: updateError } = await supabase
      .from('ad_analyses')
      .update({
        marketing_analysis: analysisData,
        status: 'completed',
        analysis_completed_at: new Date().toISOString(),
      })
      .eq('id', analysis.id)  // ← Use the actual UUID from the fetched analysis
      .select()
      .single();

    if (updateError || !updatedData) {
      console.error('❌ Failed to save analysis to Supabase:', updateError);
      // If the update fails, we must set the status to 'failed' to prevent client-side loops
      await supabase
        .from('ad_analyses')
        .update({ status: 'failed' })
        .eq('id', analysis.id);  // ← Use the actual UUID from the fetched analysis
      return NextResponse.json(
        { error: `Failed to update database: ${updateError?.message || 'Record not found or update failed.'}` },
        { status: 500 }
      );
    }

    console.log('✅ Successfully saved analysis to database for ID:', updatedData.id);

    return NextResponse.json(
      { 
        message: 'Vertex analysis completed and saved successfully',
        analysis_id: analysis.id,  // ← Use the actual UUID
        slug: analysis.slug,
        status: 'completed',
        data: analysisData
      },
      { status: 200 }
    )
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error in trigger-vertex-analysis:', {
      errorMessage,
      errorObject: error,
    });
    
    return NextResponse.json(
      { error: `An internal server error occurred: ${errorMessage}` },
      { status: 500 }
    )
  }
}