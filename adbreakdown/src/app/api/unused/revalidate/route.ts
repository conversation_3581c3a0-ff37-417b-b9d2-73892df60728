import { NextRequest, NextResponse } from 'next/server'
import { revalidatePath } from 'next/cache'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const path = searchParams.get('path')
  const secret = searchParams.get('secret')

  // Optional: Add a secret token for security
  if (process.env.REVALIDATE_SECRET && secret !== process.env.REVALIDATE_SECRET) {
    return NextResponse.json({ message: 'Invalid secret' }, { status: 401 })
  }

  if (!path) {
    return NextResponse.json({ message: 'Path parameter required' }, { status: 400 })
  }

  try {
    console.log(`🔄 Revalidating path: ${path}`)
    revalidatePath(path)
    
    return NextResponse.json({ 
      message: `Path ${path} revalidated successfully`,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Revalidation error:', error)
    return NextResponse.json(
      { message: 'Error revalidating path', error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { paths, secret } = await request.json()

    // Optional: Add a secret token for security
    if (process.env.REVALIDATE_SECRET && secret !== process.env.REVALIDATE_SECRET) {
      return NextResponse.json({ message: 'Invalid secret' }, { status: 401 })
    }

    if (!paths || !Array.isArray(paths)) {
      return NextResponse.json({ message: 'Paths array required' }, { status: 400 })
    }

    const results = []
    
    for (const path of paths) {
      try {
        console.log(`🔄 Revalidating path: ${path}`)
        revalidatePath(path)
        results.push({ path, status: 'success' })
      } catch (error) {
        console.error(`Error revalidating ${path}:`, error)
        results.push({ path, status: 'error', error: error instanceof Error ? error.message : 'Unknown error' })
      }
    }

    return NextResponse.json({ 
      message: `Revalidated ${results.length} paths`,
      results,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Bulk revalidation error:', error)
    return NextResponse.json(
      { message: 'Error in bulk revalidation', error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}