import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(req: NextRequest) {
  try {
    // Check daily_showcases table
    const { data: showcases, error: showcaseError } = await supabase
      .from('daily_showcases')
      .select('*')
      .order('featured_date', { ascending: false })

    // Check ad_analyses with showcase = true
    const { data: showcaseAnalyses, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('id, title, inferred_brand, is_public, showcase, created_at')
      .eq('showcase', true)

    // Check all public analyses
    const { data: publicAnalyses, error: publicError } = await supabase
      .from('ad_analyses')
      .select('id, title, inferred_brand, is_public, showcase, created_at')
      .eq('is_public', true)
      .limit(10)

    return NextResponse.json({
      showcases: {
        count: showcases?.length || 0,
        data: showcases,
        error: showcaseError?.message
      },
      showcaseAnalyses: {
        count: showcaseAnalyses?.length || 0,
        data: showcaseAnalyses,
        error: analysisError?.message
      },
      publicAnalyses: {
        count: publicAnalyses?.length || 0,
        data: publicAnalyses,
        error: publicError?.message
      }
    })

  } catch (error) {
    console.error('Debug showcases error:', error)
    return NextResponse.json(
      { error: 'Debug endpoint error' },
      { status: 500 }
    )
  }
}