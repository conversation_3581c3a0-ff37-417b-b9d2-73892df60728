'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Shield, Eye, AlertTriangle, Crown, Lock, CheckCircle } from 'lucide-react'
import CreditExhaustionModal from '@/components/ui/CreditExhaustionModal'
import { useUser } from '@clerk/nextjs'

interface PrivateAnalysisInputCardProps {
  onAnalyze: (url: string) => Promise<void>
  loading: boolean
  creditsRemaining?: number
}

export default function PrivateAnalysisInputCard({ 
  onAnalyze, 
  loading, 
  creditsRemaining = 0 
}: PrivateAnalysisInputCardProps) {
  const [youtubeUrl, setYoutubeUrl] = useState('')
  const [error, setError] = useState('')
  const [showCreditModal, setShowCreditModal] = useState(false)
  const [creditError, setCreditError] = useState<{ required: number; remaining: number } | null>(null)
  const [needsGoogleAuth, setNeedsGoogleAuth] = useState(false)
  const { user } = useUser()

  // Check if user has Google OAuth connected via our direct OAuth flow
  useEffect(() => {
    const checkGoogleOAuthStatus = async () => {
      if (!user) return

      try {
        const response = await fetch('/api/auth/google/status')
        if (response.ok) {
          const data = await response.json()
          setNeedsGoogleAuth(!data.connected)
          console.log('🔍 Google OAuth status:', data)
        } else {
          setNeedsGoogleAuth(true)
          console.log('🔍 No Google OAuth connection found')
        }
      } catch (error) {
        console.error('❌ Error checking OAuth status:', error)
        setNeedsGoogleAuth(true)
      }
    }

    checkGoogleOAuthStatus()
  }, [user])

  const handleSubmit = async () => {
    if (!youtubeUrl.trim()) {
      setError('Please enter a YouTube URL')
      return
    }

    // Check if Google auth is connected
    if (needsGoogleAuth) {
      setError('Please connect your Google account to analyze videos')
      return
    }

    // Check credits before proceeding
    if (creditsRemaining <= 0) {
      setCreditError({ required: 1, remaining: creditsRemaining })
      setShowCreditModal(true)
      return
    }

    // Validate YouTube URL format
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)/
    if (!youtubeRegex.test(youtubeUrl)) {
      setError('Please enter a valid YouTube URL')
      return
    }

    setError('')
    try {
      await onAnalyze(youtubeUrl)
      setYoutubeUrl('')
    } catch (err: any) {
      // Handle credit exhaustion specifically
      if (err.message && err.message.includes('Insufficient credits')) {
        setCreditError({ required: 1, remaining: creditsRemaining })
        setShowCreditModal(true)
      } else if (err.message && err.message.includes('Video duration')) {
        setError(`${err.message}. Videos longer than 3 minutes require admin review.`)
      } else if (err.message && err.message.includes('OAuth')) {
        setNeedsGoogleAuth(true)
        setError('Please connect your Google account to access this video')
      } else {
        setError(err.message || 'An error occurred while processing your request')
      }
    }
  }

  const handleConnectGoogle = () => {
    try {
      console.log('🔗 Initiating direct Google OAuth flow...')
      setError('')
      
      // Redirect to our OAuth initiation endpoint
      window.location.href = '/api/auth/google/initiate'
      
    } catch (error: any) {
      console.error('❌ Error initiating OAuth:', error)
      setError('Failed to initiate Google connection. Please try again.')
    }
  }

  // Check for OAuth success/error on page load
  useEffect(() => {
    const checkForOAuthResult = async () => {
      const urlParams = new URLSearchParams(window.location.search)
      
      if (urlParams.get('oauth_success') === 'true') {
        console.log('✅ OAuth connection successful!')
        setNeedsGoogleAuth(false)
        // Clear the URL parameter
        window.history.replaceState({}, document.title, window.location.pathname)
        // Refresh OAuth status
        const response = await fetch('/api/auth/google/status')
        if (response.ok) {
          const data = await response.json()
          setNeedsGoogleAuth(!data.connected)
        }
      }
      
      if (urlParams.get('oauth_error')) {
        const errorType = urlParams.get('oauth_error')
        console.error('❌ OAuth error:', errorType)
        setError(`OAuth connection failed: ${errorType}`)
        // Clear the URL parameter
        window.history.replaceState({}, document.title, window.location.pathname)
      }
    }
    
    checkForOAuthResult()
  }, [])

  return (
    <>
      <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Shield className="h-6 w-6 text-purple-600" />
            <CardTitle className="text-purple-900">Private Analysis</CardTitle>
            <div className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs font-medium">
              BETA
            </div>
          </div>
          <CardDescription className="text-purple-700">
            Pre-launch analysis for private and unlisted videos. Perfect for testing ads before they go live.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {needsGoogleAuth && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-800">Google Account Required</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Connect your Google account to access private video analysis features.
                  </p>
                  <Button
                    onClick={handleConnectGoogle}
                    variant="outline"
                    size="sm"
                    className="mt-2 border-yellow-300 text-yellow-800 hover:bg-yellow-100"
                  >
                    Connect Google Account
                  </Button>
                </div>
              </div>
            </div>
          )}

          {!needsGoogleAuth && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-800">Google Account Connected</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Ready for private analysis. Currently testing with public videos - unlisted support coming with enhanced YouTube scopes.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Lock className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-800">Privacy Features</h4>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Analyse ad creative before they go live</li>
                  <li>• Get pre-launch optimization recommendations</li>
                  <li>• Analysis results remain private to your account</li>
                  <li>• Access to unlisted YouTube videos (coming soon)</li>
                  <li>• Chat based ad agent to ask questions (coming soon)</li>

                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Input
              type="url"
              placeholder="Enter YouTube URL (testing with public videos - unlisted support coming soon)"
              value={youtubeUrl}
              onChange={(e) => {
                setYoutubeUrl(e.target.value)
                setError('')
              }}
              className="border-purple-200 focus:border-purple-400"
              disabled={loading || needsGoogleAuth}
            />
            {error && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertTriangle className="h-4 w-4" />
                {error}
              </p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-purple-600">
              <Crown className="h-4 w-4 inline mr-1" />
              Credits remaining: {creditsRemaining}
            </div>
            <Button 
              onClick={handleSubmit}
              disabled={loading || !youtubeUrl.trim() || needsGoogleAuth}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Analyzing...
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Analyze Privately
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {creditError && (
        <CreditExhaustionModal
          isOpen={showCreditModal}
          onClose={() => {
            setShowCreditModal(false)
            setCreditError(null)
          }}
          creditsRequired={creditError.required}
          creditsRemaining={creditError.remaining}
        />
      )}
    </>
  )
}