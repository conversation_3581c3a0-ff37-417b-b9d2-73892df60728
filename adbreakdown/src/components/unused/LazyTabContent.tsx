'use client'

import React, { useRef } from 'react'
import { useTabLazyLoading, useIntersectionObserver } from '@/hooks/useProgressiveLoading'
import { TabContentSkeleton } from '@/components/ui/skeleton'

interface LazyTabContentProps {
  activeTab: string
  tabName: string
  children: React.ReactNode
  loadOnScroll?: boolean
  fallback?: React.ReactNode
}

export default function LazyTabContent({
  activeTab,
  tabName,
  children,
  loadOnScroll = false,
  fallback = <TabContentSkeleton />
}: LazyTabContentProps) {
  const ref = useRef<HTMLDivElement>(null)
  const { shouldLoad: shouldLoadTab } = useTabLazyLoading(activeTab, tabName)
  const { hasBeenVisible } = useIntersectionObserver(ref, { threshold: 0.1 })

  const shouldLoad = shouldLoadTab || (loadOnScroll && hasBeenVisible)

  return (
    <div ref={ref}>
      {shouldLoad ? children : fallback}
    </div>
  )
}