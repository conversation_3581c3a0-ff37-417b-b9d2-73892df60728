'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Caprasimo } from 'next/font/google'

const caprasimo = Caprasimo({
  weight: '400',
  subsets: ['latin'],
  display: 'swap',
})

interface LogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
}

export default function Logo({ 
  className = '', 
  size = 'md', 
  showText = true 
}: LogoProps) {
  const sizeClasses = {
    sm: {
      image: 'w-6 h-6',
      text: 'text-lg font-medium'
    },
    md: {
      image: 'w-8 h-8',
      text: 'text-2xl font-medium'
    },
    lg: {
      image: 'w-10 h-10',
      text: 'text-3xl font-semibold'
    }
  }

  const currentSize = sizeClasses[size]

  return (
    <Link 
      href="/" 
      className={`flex items-center space-x-3 hover:opacity-80 transition-opacity ${className}`}
    >
      <Image
        src="/logo.png"
        alt="AdBreakdown Logo"
        width={size === 'sm' ? 24 : size === 'md' ? 32 : 40}
        height={size === 'sm' ? 24 : size === 'md' ? 32 : 40}
        className={`${currentSize.image} object-contain`}
        priority
      />
      {showText && (
        <span className={`${currentSize.text} text-orange-500 ${caprasimo.className}`}>
          breakdown.ad
        </span>
      )}
    </Link>
  )
}