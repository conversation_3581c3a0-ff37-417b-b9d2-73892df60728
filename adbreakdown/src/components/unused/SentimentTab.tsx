
'use client'

import React, { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeSanitize from 'rehype-sanitize'
import { Skeleton } from '@/components/ui/skeleton'

interface SentimentTabProps {
  detailedAnalysisData: any
  emotionTimeline: any
}

const SentimentTab: React.FC<SentimentTabProps> = ({ detailedAnalysisData, emotionTimeline }) => {
  return (
    <Suspense fallback={<div className="space-y-4">{[...Array(3)].map((_, i) => <Skeleton key={i} className="h-32 w-full" />)}</div>}>
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Emotion Distribution</CardTitle>
          <CardDescription>Breakdown of emotions detected throughout the ad</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(detailedAnalysisData.emotions).map(([emotion, percentage]: [string, any]) => (
              <div key={emotion} className="flex items-center gap-4">
                <div className="w-20 text-sm font-medium capitalize">{emotion}</div>
                <div className="flex-1"><Progress value={percentage} className="h-2" /></div>
                <div className="w-12 text-sm text-gray-600">{percentage}%</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="relative">
        <CardHeader>
          <CardTitle>Sentiment Timeline</CardTitle>
          <CardDescription>How sentiment changes throughout the ad duration</CardDescription>
        </CardHeader>
        <CardContent className="relative">
          <div className="h-64 flex items-end gap-2 bg-gray-100 rounded-lg p-4 blur-sm">
            {detailedAnalysisData.scriptAnalysis.sentimentTimeline.map((point: any, index: number) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div 
                  className="w-full bg-blue-500 rounded-t" 
                  style={{ height: `${point.sentiment * 200}px` }}
                />
                <div className="text-xs text-gray-600 mt-1">
                  {Math.floor(point.time / 60)}:{(point.time % 60).toString().padStart(2, "0")}
                </div>
              </div>
            ))}
          </div>
          {/* Coming Soon Overlay */}
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
            <div className="text-center p-6">
              <div className="text-2xl font-bold text-gray-800 mb-2">Coming Soon</div>
              <div className="text-sm text-gray-600 max-w-xs">
                Interactive sentiment timeline visualization and analysis tools are in development
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {emotionTimeline && (
        <Card className="mt-6 relative">
          <CardHeader>
            <CardTitle>Advanced Emotion Timeline</CardTitle>
            <CardDescription>Detailed emotional journey of the ad</CardDescription>
          </CardHeader>
          <CardContent className="relative">
            <div className="blur-sm">
              <div className="text-sm text-gray-900 leading-relaxed tracking-normal space-y-2 mb-4">
                <ReactMarkdown 
                  remarkPlugins={[remarkGfm]} 
                  rehypePlugins={[rehypeSanitize]}
                  components={{
                    p: ({children}) => <p className="text-sm text-gray-900 leading-relaxed tracking-normal mb-2">{children}</p>,
                    strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                    em: ({children}) => <em className="italic text-gray-700">{children}</em>
                  }}
                >
                  {emotionTimeline.analysisSummary || 'Detailed emotion analysis of the ad content.'}
                </ReactMarkdown>
              </div>

              {/* Overall Sentiment and Primary Emotion */}
              {(emotionTimeline.overallSentiment || emotionTimeline.primaryAudienceEmotion) && (
                <div className="grid grid-cols-2 gap-4 mb-6">
                  {emotionTimeline.overallSentiment && (
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-1">Overall Sentiment</h4>
                      <p className="text-lg font-bold text-blue-700">{emotionTimeline.overallSentiment}</p>
                    </div>
                  )}
                  {emotionTimeline.primaryAudienceEmotion && (
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-900 mb-1">Primary Emotion</h4>
                      <p className="text-lg font-bold text-green-700">{emotionTimeline.primaryAudienceEmotion}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Timeline Events */}
              {emotionTimeline.timeline && Array.isArray(emotionTimeline.timeline) && emotionTimeline.timeline.length > 0 && (
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 mb-3">Timeline Events</h4>
                  {emotionTimeline.timeline.map((event: any, index: number) => (
                    <div key={event.event_id || index} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className="font-mono text-xs">
                            {Math.floor(event.start_time_seconds / 60)}:{(event.start_time_seconds % 60).toString().padStart(2, '0')} - 
                            {Math.floor(event.end_time_seconds / 60)}:{(event.end_time_seconds % 60).toString().padStart(2, '0')}
                          </Badge>
                          <Badge 
                            className={`text-xs ${
                              event.intensity === 'Peak' ? 'bg-red-100 text-red-800' :
                              event.intensity === 'High' ? 'bg-orange-100 text-orange-800' :
                              event.intensity === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {event.intensity}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${
                              event.audience_emotion === 'Joy' ? 'bg-green-100 text-green-800' :
                              event.audience_emotion === 'Humor' ? 'bg-blue-100 text-blue-800' :
                              event.audience_emotion === 'Surprise' ? 'bg-purple-100 text-purple-800' :
                              event.audience_emotion === 'Anger' ? 'bg-red-100 text-red-800' :
                              event.audience_emotion === 'Sadness' ? 'bg-gray-100 text-gray-800' :
                              event.audience_emotion === 'Fear' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-700'
                            }`}
                          >
                            Audience: {event.audience_emotion}
                          </Badge>
                          {event.actor_emotion && (
                            <Badge variant="outline" className="text-xs">
                              Actor: {event.actor_emotion}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-900 leading-relaxed tracking-normal">
                        {event.event_description}
                      </p>
                    </div>
                  ))}
                </div>
              )}

              {/* Fallback if no timeline data */}
              {(!emotionTimeline.timeline || !Array.isArray(emotionTimeline.timeline) || emotionTimeline.timeline.length === 0) && (
                <div className="h-40 bg-gray-50 rounded-lg p-4 flex items-center justify-center">
                  <p className="text-center text-gray-500">No timeline events available.</p>
                </div>
              )}
            </div>
            {/* Coming Soon Overlay */}
            <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
              <div className="text-center p-6">
                <div className="text-2xl font-bold text-gray-800 mb-2">Coming Soon</div>
                <div className="text-sm text-gray-600 max-w-xs">
                  Advanced emotion timeline analysis and visualization features are in development
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </Suspense>
  )
}

export default SentimentTab
