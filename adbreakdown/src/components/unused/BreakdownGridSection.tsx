'use client';

import React, { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Eye, Volume2, Clock, Palette } from 'lucide-react'

interface BreakdownGridSectionProps {
  detailedAnalysisData: any
}

const BreakdownGridSection: React.FC<BreakdownGridSectionProps> = ({ detailedAnalysisData }) => {
  return (
    <Suspense fallback={<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">{[...Array(2)].map((_, i) => <Skeleton key={i} className="h-96 w-full" />)}</div>}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Visual Elements - Left Column */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Visual Elements
            </CardTitle>
            <CardDescription>Scene breakdown, visual composition, and color analysis</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Visual Appeal Score */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Visual Appeal</h4>
              <div className="flex items-center gap-3">
                <Progress value={detailedAnalysisData.visualAnalysis.visualAppeal * 10} className="flex-1" />
                <span className="font-bold text-lg text-blue-600">{detailedAnalysisData.visualAnalysis.visualAppeal}/10</span>
              </div>
              <p className="text-sm text-gray-600">Professional production quality with strong visual impact</p>
            </div>

            {/* Color Palette */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                <h4 className="font-medium text-sm">Color Palette</h4>
              </div>
              <div className="flex gap-2 flex-wrap">
                {detailedAnalysisData.visualAnalysis.colorPalette.map((color: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <div 
                      className="w-6 h-6 rounded border border-gray-300" 
                      style={{ backgroundColor: color }}
                    ></div>
                    <span className="text-xs text-gray-600 font-mono">{color}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Scene Breakdown */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Scene Breakdown</h4>
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {detailedAnalysisData.visualAnalysis.scenes.map((scene: any, index: number) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3 text-gray-500" />
                        <span className="text-xs font-medium text-gray-700">{scene.time}s</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        Scene {index + 1}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{scene.description}</p>
                    {scene.objects && scene.objects.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {scene.objects.map((obj: string, objIndex: number) => (
                          <Badge key={objIndex} variant="secondary" className="text-xs">
                            {obj}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Audio Elements - Right Column */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              Audio Elements
            </CardTitle>
            <CardDescription>Sound design, music, and audio quality analysis</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Audio Quality Score */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Audio Quality</h4>
              <div className="flex items-center gap-3">
                <Progress value={detailedAnalysisData.audioAnalysis.audioQuality * 10} className="flex-1" />
                <span className="font-bold text-lg text-green-600">{detailedAnalysisData.audioAnalysis.audioQuality}/10</span>
              </div>
              <p className="text-sm text-gray-600">Clear sound mixing with professional audio production</p>
            </div>

            {/* Music Mood */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Music Mood</h4>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-900 font-medium">{detailedAnalysisData.audioAnalysis.musicMood}</p>
              </div>
            </div>

            {/* Voice Tone */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Voice Tone</h4>
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                <p className="text-sm text-purple-900 font-medium">{detailedAnalysisData.audioAnalysis.voiceTone}</p>
              </div>
            </div>

            {/* Audio Elements */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Audio Components</h4>
              <div className="space-y-2">
                {detailedAnalysisData.audioAnalysis.soundEffects?.map((effect: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-700 capitalize">{effect}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Audio Analysis Summary */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <p className="text-sm text-gray-700">
                Professional audio production with clear dialogue, appropriate background music, and effective sound design that enhances the overall message delivery.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </Suspense>
  )
}

export default BreakdownGridSection