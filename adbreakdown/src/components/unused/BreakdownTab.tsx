
'use client'

import React, { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Eye, Volume2 } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

interface BreakdownTabProps {
  detailedAnalysisData: any
}

const BreakdownTab: React.FC<BreakdownTabProps> = ({ detailedAnalysisData }) => {
  return (
    <Suspense fallback={<div className="space-y-4">{[...Array(3)].map((_, i) => <Skeleton key={i} className="h-40 w-full" />)}</div>}>
      <div className="space-y-4">
        {/* Visual Analysis Section */}
        <Card className="relative">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Visual Elements
            </CardTitle>
            <CardDescription>Scene breakdown, visual composition, and color analysis</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 relative">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Visual Appeal Score */}
              <div className="space-y-3">
                <h4 className="font-medium">Visual Appeal</h4>
                <div className="flex items-center gap-3">
                  <Progress value={detailedAnalysisData.visualAnalysis.visualAppeal * 10} className="flex-1" />
                  <span className="font-bold text-lg text-blue-600">{detailedAnalysisData.visualAnalysis.visualAppeal}/10</span>
                </div>
                <p className="text-sm text-gray-600">Professional production quality with strong visual impact</p>
              </div>

              {/* Color Palette */}
              <div className="space-y-3">
                <h4 className="font-medium">Color Palette</h4>
                <div className="flex gap-2">
                  {detailedAnalysisData.visualAnalysis.colorPalette.map((color: string, index: number) => (
                    <div key={index} className="flex flex-col items-center gap-1">
                      <div className="w-8 h-8 rounded-lg border shadow-sm" style={{ backgroundColor: color }} />
                      <span className="text-xs text-gray-600 font-mono">{color}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Scene Breakdown */}
            <div>
              <h4 className="font-medium mb-3">Scene Breakdown</h4>
              <div className="space-y-3">
                {detailedAnalysisData.visualAnalysis.scenes.map((scene: any, index: number) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-2">
                      <h5 className="font-medium text-gray-900">Scene {index + 1}</h5>
                      <Badge variant="outline" className="font-mono text-xs">
                        {Math.floor(scene.time / 60)}:{(scene.time % 60).toString().padStart(2, "0")}
                      </Badge>
                    </div>
                    <p className="text-gray-700 mb-2 text-sm">{scene.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {scene.objects.map((object: string, objIndex: number) => (
                        <Badge key={objIndex} variant="secondary" className="text-xs">
                          {object}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Coming Soon Overlay for Visual Analysis */}
            <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
              <div className="text-center p-6">
                <div className="text-2xl font-bold text-gray-800 mb-2">Coming Soon</div>
                <div className="text-sm text-gray-600 max-w-xs">
                  Advanced visual analysis with object detection, scene classification, and visual appeal metrics are in development
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Audio Analysis Section */}
        <Card className="relative">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              Audio Elements
            </CardTitle>
            <CardDescription>Voice tone, music analysis, and sound design</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 relative">
            {/* Audio Quality and Characteristics */}
            <div className="grid md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <h4 className="font-medium">Audio Quality</h4>
                <div className="flex items-center gap-3">
                  <Progress value={detailedAnalysisData.audioAnalysis.audioQuality * 10} className="flex-1" />
                  <span className="font-bold text-lg text-purple-600">{detailedAnalysisData.audioAnalysis.audioQuality}/10</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">Music Mood</h4>
                <Badge className="bg-purple-100 text-purple-800 w-full justify-center">
                  {detailedAnalysisData.audioAnalysis.musicMood || 'Not specified'}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">Voice Tone</h4>
                <Badge className="bg-blue-100 text-blue-800 w-full justify-center">
                  {detailedAnalysisData.audioAnalysis.voiceTone || 'Not specified'}
                </Badge>
              </div>
            </div>

            {/* Sound Effects */}
            <div>
              <h4 className="font-medium mb-3">Audio Elements</h4>
              <div className="flex flex-wrap gap-2">
                {detailedAnalysisData.audioAnalysis.soundEffects.map((effect: string, index: number) => (
                  <Badge key={index} variant="outline" className="capitalize">
                    {effect}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Audio-Visual Harmony */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium mb-2 text-blue-900 flex items-center gap-2">
                🎵 Audio-Visual Harmony
              </h4>
              <p className="text-sm text-blue-800">
                The audio and visual elements work together to create a cohesive brand experience. 
                The {detailedAnalysisData.audioAnalysis.musicMood?.toLowerCase()} music complements the 
                visual tone, while the {detailedAnalysisData.audioAnalysis.voiceTone?.toLowerCase()} voiceover 
                enhances the overall message delivery.
              </p>
            </div>
            
            {/* Coming Soon Overlay for Audio Analysis */}
            <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
              <div className="text-center p-6">
                <div className="text-2xl font-bold text-gray-800 mb-2">Coming Soon</div>
                <div className="text-sm text-gray-600 max-w-xs">
                  Advanced audio analysis with speech pattern recognition, music classification, and sound quality metrics are in development
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Suspense>
  )
}

export default BreakdownTab
