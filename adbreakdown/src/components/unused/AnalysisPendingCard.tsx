'use client'

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface AnalysisPendingCardProps {
  onStartAnalysis: () => void
  loading: boolean
}

export default function AnalysisPendingCard({
  onStartAnalysis,
  loading
}: AnalysisPendingCardProps) {
  return (
    <Card className="mb-8 text-center">
      <CardHeader>
        <CardTitle>Analysis Pending</CardTitle>
        <CardDescription>The initial analysis for this video has not been run yet.</CardDescription>
      </CardHeader>
      <CardContent>
        <Button onClick={onStartAnalysis} disabled={loading}>
          {loading ? 'Starting Analysis...' : 'Start Initial Analysis (1 Credit)'}
        </Button>
      </CardContent>
    </Card>
  )
}