
'use client'

import React, { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Users, BarChart3 } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

interface AudienceTabProps {
  detailedAnalysisData: any
}

const AudienceTab: React.FC<AudienceTabProps> = ({ detailedAnalysisData }) => {
  const targeting = detailedAnalysisData.targetingRecommendations

  return (
    <Suspense fallback={<div className="space-y-4">{[...Array(2)].map((_, i) => <Skeleton key={i} className="h-48 w-full" />)}</div>}>
      <div className="grid md:grid-cols-2 gap-4">
        <Card className="relative">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Targeting Recommendations
            </CardTitle>
            <CardDescription>AI-powered suggestions for reaching the right audience</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 relative">
            <div className="grid md:grid-cols-3 gap-6">
              {/* Demographics */}
              <div className="space-y-3">
                <h4 className="font-medium">Demographics</h4>
                <div className="flex flex-wrap gap-2">
                  {targeting.demographics.map((item: string, index: number) => (
                    <Badge key={index} variant="secondary">{item}</Badge>
                  ))}
                </div>
              </div>

              {/* Interests */}
              <div className="space-y-3">
                <h4 className="font-medium">Interests</h4>
                <div className="flex flex-wrap gap-2">
                  {targeting.interests.map((item: string, index: number) => (
                    <Badge key={index} variant="secondary">{item}</Badge>
                  ))}
                </div>
              </div>

              {/* Behaviors */}
              <div className="space-y-3">
                <h4 className="font-medium">Behaviors</h4>
                <div className="flex flex-wrap gap-2">
                  {targeting.behaviors.map((item: string, index: number) => (
                    <Badge key={index} variant="secondary">{item}</Badge>
                  ))}
                </div>
              </div>
            </div>
            
            
          </CardContent>
        </Card>
        <Card className="relative">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Key Themes
            </CardTitle>
            <CardDescription>The core messages and themes that resonate with the target audience</CardDescription>
          </CardHeader>
          <CardContent className="relative">
            <div className="flex flex-wrap gap-2">
              {detailedAnalysisData.scriptAnalysis.keyThemes.map((theme: string, index: number) => (
                <Badge key={index} className="text-base px-4 py-2">
                  {theme}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Suspense>
  )
}

export default AudienceTab
