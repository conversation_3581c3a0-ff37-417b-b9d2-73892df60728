'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft, 
  Share2, 
  Bookmark, 
  Heart,
  Eye,
  Calendar,
  Hash,
  Globe,
  Lock,
  Plus
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { UserButton } from '@clerk/nextjs'

interface AnalysisPageHeaderProps {
  // Analysis context
  analysis?: {
    id: string
    slug?: string
    title: string
    inferred_brand?: string
    is_public: boolean
    created_at: string
    view_count?: number
    like_count?: number
    user_id?: string
  }
  
  // Navigation context
  referrer?: 'ad-library' | 'dashboard' | 'daily-showcase' | 'home'
  
  // Page actions
  onShare?: () => void
  onBookmark?: () => void
  onTogglePublic?: () => void
  
  className?: string
}

export default function AnalysisPageHeader({
  analysis,
  referrer = 'ad-library',
  onShare,
  onBookmark,
  onTogglePublic,
  className = ''
}: AnalysisPageHeaderProps) {
  const { isAuthenticated, user } = useAuth()
  const { profile } = useCredits()

  // Determine back navigation based on referrer and auth state
  const getBackNavigation = () => {
    if (!isAuthenticated) {
      return { href: '/ad-library', text: 'Ad Library' }
    }
    
    switch (referrer) {
      case 'dashboard':
        return { href: '/dashboard', text: 'My Dashboard' }
      case 'daily-showcase':
        return { href: '/daily-showcase', text: 'Daily Showcase' }
      case 'home':
        return { href: '/ad-library', text: 'Ad Library' }
      default:
        return { href: '/ad-library', text: 'Ad Library' }
    }
  }

  const backNav = getBackNavigation()
  const isOwner = isAuthenticated && user?.id === analysis?.user_id
  const isPublic = analysis?.is_public

  return (
    <header className={`border-b bg-white/95 backdrop-blur-sm sticky top-0 z-40 ${className}`}>
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          
          {/* Left: Navigation & Brand */}
          <div className="flex items-center gap-4">
            <Link href={backNav.href}>
              <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {backNav.text}
              </Button>
            </Link>
            
            <Separator orientation="vertical" className="h-6" />
            
            <Link href="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
              <Image
                src="/logo.png"
                alt="AdBreakdown"
                width={24}
                height={24}
                className="w-6 h-6 object-contain"
                priority
              />
              <span className="font-semibold text-gray-900 hidden sm:block">AdBreakdown</span>
            </Link>
          </div>

          {/* Center: Analysis Context (on larger screens) */}
          {analysis && (
            <div className="hidden lg:flex items-center gap-3 flex-1 justify-center max-w-md mx-8">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-1">
                  {isPublic ? (
                    <Globe className="h-4 w-4 text-green-600" />
                  ) : (
                    <Lock className="h-4 w-4 text-gray-500" />
                  )}
                  <span className="text-sm font-medium text-gray-900 truncate">
                    {analysis.inferred_brand || 'Unknown Brand'}
                  </span>
                </div>
                <div className="flex items-center justify-center gap-3 text-xs text-gray-500">
                  {analysis.view_count && (
                    <span className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      {analysis.view_count.toLocaleString()}
                    </span>
                  )}
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(analysis.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Right: Actions & User */}
          <div className="flex items-center gap-2">
            
            {/* Analysis Actions */}
            {analysis && (
              <div className="flex items-center gap-1">
                
                {/* Share Button */}
                {isPublic && onShare && (
                  <Button variant="ghost" size="sm" onClick={onShare} className="hidden sm:flex">
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                )}
                
                {/* Bookmark (for authenticated users viewing public content) */}
                {isAuthenticated && !isOwner && onBookmark && (
                  <Button variant="ghost" size="sm" onClick={onBookmark}>
                    <Bookmark className="h-4 w-4" />
                  </Button>
                )}
                
                {/* Public/Private Toggle (for owners) */}
                {isOwner && onTogglePublic && (
                  <Button variant="outline" size="sm" onClick={onTogglePublic}>
                    {isPublic ? (
                      <>
                        <Globe className="h-4 w-4 mr-1" />
                        <span className="hidden sm:inline">Public</span>
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        <span className="hidden sm:inline">Private</span>
                      </>
                    )}
                  </Button>
                )}
              </div>
            )}

            {/* Authenticated User Actions */}
            {isAuthenticated ? (
              <div className="flex items-center gap-2">
                <Separator orientation="vertical" className="h-6" />
                
                {/* Create New Analysis */}
                <Link href="/dashboard">
                  <Button size="sm" variant="default" className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">New Analysis</span>
                  </Button>
                </Link>
                
                {/* Credits Badge */}
                {profile && (
                  <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                    {profile.credits_remaining} credits
                  </Badge>
                )}
                
                {/* User Menu */}
                <UserButton 
                  afterSignOutUrl="/"
                  appearance={{
                    elements: {
                      avatarBox: "w-8 h-8"
                    }
                  }}
                />
              </div>
            ) : (
              /* Unauthenticated User Actions */
              <div className="flex items-center gap-2">
                <Link href="/sign-in">
                  <Button variant="ghost" size="sm">
                    Sign In
                  </Button>
                </Link>
                <Link href="/sign-up">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    Sign Up Free
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
        
        {/* Mobile Analysis Context */}
        {analysis && (
          <div className="lg:hidden mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {isPublic ? (
                  <Globe className="h-4 w-4 text-green-600" />
                ) : (
                  <Lock className="h-4 w-4 text-gray-500" />
                )}
                <span className="text-sm font-medium text-gray-900">
                  {analysis.inferred_brand || 'Unknown Brand'}
                </span>
              </div>
              <div className="flex items-center gap-3 text-xs text-gray-500">
                {analysis.view_count && (
                  <span className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {analysis.view_count > 999 ? `${Math.round(analysis.view_count/1000)}k` : analysis.view_count}
                  </span>
                )}
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {new Date(analysis.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}