'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Eye, ThumbsUp, MessageSquare, Calendar, Hash, Globe } from 'lucide-react'
import { CampaignAnalysisSkeleton } from '@/components/ui/skeleton'

interface CampaignAnalysisSectionProps {
  analysisId: string
  basicAnalysis: any
}

export default function CampaignAnalysisSection({ 
  analysisId, 
  basicAnalysis 
}: CampaignAnalysisSectionProps) {
  const metadata = basicAnalysis?.metadata
  const isLoading = false

  if (isLoading || !metadata) {
    return (
      <Card>
        <CampaignAnalysisSkeleton />
      </Card>
    )
  }

  const youtubeMetadata = metadata.youtubeMetadata

  if (!youtubeMetadata) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg mb-2 flex items-center gap-2">
              <Globe className="h-5 w-5 text-red-600" />
              {youtubeMetadata.channelTitle}
            </CardTitle>
            <CardDescription className="text-sm text-gray-600">
              Campaign Analysis • Published {new Date(youtubeMetadata.publishedAt).toLocaleDateString()}
            </CardDescription>
          </div>
          {basicAnalysis.inferred_brand && basicAnalysis.inferred_brand !== 'N/A' && (
            <Badge variant="outline" className="ml-4 text-xs">
              Brand: {basicAnalysis.inferred_brand}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {/* Video Stats */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Eye className="h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-600">Views</span>
            </div>
            <div className="text-sm font-bold text-gray-900">
              {parseInt(youtubeMetadata.viewCount).toLocaleString()}
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <ThumbsUp className="h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-600">Likes</span>
            </div>
            <div className="text-sm font-bold text-gray-900">
              {parseInt(youtubeMetadata.likeCount).toLocaleString()}
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <MessageSquare className="h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-600">Comments</span>
            </div>
            <div className="text-sm font-bold text-gray-900">
              {parseInt(youtubeMetadata.commentCount).toLocaleString()}
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Calendar className="h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-600">Quality</span>
            </div>
            <div className="text-sm font-bold text-gray-900">
              {youtubeMetadata.definition.toUpperCase()}
            </div>
          </div>
        </div>

        {/* Video Description */}
        {youtubeMetadata.description && (
          <div className="mb-4">
            <h4 className="font-medium mb-2 text-gray-900 text-sm">Video Description</h4>
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-xs text-gray-700 leading-relaxed line-clamp-3 whitespace-pre-wrap">
                {youtubeMetadata.description.length > 200 
                  ? `${youtubeMetadata.description.substring(0, 200)}...` 
                  : youtubeMetadata.description
                }
              </p>
            </div>
          </div>
        )}

        {/* Tags */}
        {youtubeMetadata.tags && youtubeMetadata.tags.length > 0 && (
          <div>
            <h4 className="font-medium mb-2 text-gray-900 flex items-center gap-2 text-sm">
              <Hash className="h-3 w-3" />
              Video Tags
            </h4>
            <div className="flex flex-wrap gap-1">
              {youtubeMetadata.tags.slice(0, 6).map((tag: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs px-2 py-0.5">
                  {tag}
                </Badge>
              ))}
              {youtubeMetadata.tags.length > 6 && (
                <Badge variant="outline" className="text-xs px-2 py-0.5">
                  +{youtubeMetadata.tags.length - 6} more
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}