'use client'

import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { cn } from '@/lib/utils'

interface MarkdownRendererProps {
  content: string
  variant?: 'default' | 'analysis' | 'compact'
  className?: string
}

const variants = {
  default: {
    container: 'prose prose-sm max-w-none',
    components: {
      h1: ({ children }: any) => <h1 className="text-2xl font-bold text-gray-900 mt-8 mb-4 pb-2 border-b-2 border-gray-200">{children}</h1>,
      h2: ({ children }: any) => <h2 className="text-xl font-bold text-gray-900 mt-6 mb-3 pb-1 border-b border-gray-200">{children}</h2>,
      h3: ({ children }: any) => <h3 className="text-lg font-semibold text-gray-800 mt-5 mb-2">{children}</h3>,
      h4: ({ children }: any) => <h4 className="text-base font-medium text-gray-700 mt-4 mb-2">{children}</h4>,
      p: ({ children }: any) => <p className="mb-3 text-gray-700 leading-relaxed">{children}</p>,
      ul: ({ children }: any) => <ul className="mb-3 space-y-1 list-disc list-inside">{children}</ul>,
      li: ({ children }: any) => <li className="text-gray-700 leading-relaxed ml-4">{children}</li>,
      strong: ({ children }: any) => <strong className="font-semibold text-gray-800">{children}</strong>,
      code: ({ children }: any) => <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-800">{children}</code>,
      pre: ({ children }: any) => <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4 border border-gray-200">{children}</pre>,
      blockquote: ({ children }: any) => <blockquote className="border-l-4 border-blue-200 pl-4 py-2 bg-blue-50 italic text-gray-700 mb-4">{children}</blockquote>,
      table: ({ children }: any) => <table className="w-full border-collapse border border-gray-300 mb-4 text-sm">{children}</table>,
      th: ({ children }: any) => <th className="border border-gray-300 px-3 py-2 bg-gray-50 font-semibold text-left">{children}</th>,
      td: ({ children }: any) => <td className="border border-gray-300 px-3 py-2">{children}</td>,
    }
  },
  analysis: {
    container: 'prose prose-sm prose-purple max-w-none analysis-content',
    components: {
      h1: ({ children }: any) => <h1 className="text-2xl font-bold text-purple-900 mt-10 mb-6 pb-3 border-b-2 border-purple-200">{children}</h1>,
      h2: ({ children }: any) => <h2 className="text-xl font-bold text-purple-900 mt-8 mb-4 pb-2 border-b border-purple-200">{children}</h2>,
      h3: ({ children }: any) => <h3 className="text-lg font-semibold text-purple-800 mt-6 mb-3">{children}</h3>,
      h4: ({ children }: any) => <h4 className="text-base font-medium text-purple-700 mt-4 mb-2">{children}</h4>,
      p: ({ children }: any) => <p className="mb-4 text-gray-700 leading-relaxed">{children}</p>,
      ul: ({ children }: any) => <ul className="mb-4 space-y-1 list-disc list-inside">{children}</ul>,
      li: ({ children }: any) => <li className="text-gray-700 leading-relaxed ml-4">{children}</li>,
      strong: ({ children }: any) => <strong className="font-semibold text-purple-800">{children}</strong>,
      code: ({ children }: any) => <code className="bg-purple-100 px-2 py-1 rounded text-sm font-mono text-purple-800">{children}</code>,
      pre: ({ children }: any) => <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4 border border-gray-200">{children}</pre>,
      blockquote: ({ children }: any) => <blockquote className="border-l-4 border-purple-200 pl-4 py-2 bg-purple-50 italic text-gray-700 mb-4">{children}</blockquote>,
      table: ({ children }: any) => <table className="w-full border-collapse border border-gray-300 mb-4 text-sm">{children}</table>,
      th: ({ children }: any) => <th className="border border-gray-300 px-3 py-2 bg-purple-50 font-semibold text-left">{children}</th>,
      td: ({ children }: any) => <td className="border border-gray-300 px-3 py-2">{children}</td>,
    }
  },
  compact: {
    container: 'prose prose-xs max-w-none compact-content',
    components: {
      h1: ({ children }: any) => <h1 className="text-lg font-bold text-gray-900 mt-4 mb-2">{children}</h1>,
      h2: ({ children }: any) => <h2 className="text-base font-bold text-gray-900 mt-3 mb-2">{children}</h2>,
      h3: ({ children }: any) => <h3 className="text-sm font-semibold text-gray-800 mt-2 mb-1">{children}</h3>,
      h4: ({ children }: any) => <h4 className="text-sm font-medium text-gray-700 mt-2 mb-1">{children}</h4>,
      p: ({ children }: any) => <p className="mb-2 text-gray-700 text-sm leading-snug">{children}</p>,
      ul: ({ children }: any) => <ul className="mb-2 space-y-0 list-disc list-inside">{children}</ul>,
      li: ({ children }: any) => <li className="text-gray-700 text-sm ml-2">{children}</li>,
      strong: ({ children }: any) => <strong className="font-semibold text-gray-800">{children}</strong>,
      code: ({ children }: any) => <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono text-gray-800">{children}</code>,
      pre: ({ children }: any) => <pre className="bg-gray-100 p-2 rounded overflow-x-auto mb-2 border border-gray-200 text-xs">{children}</pre>,
      blockquote: ({ children }: any) => <blockquote className="border-l-2 border-blue-200 pl-2 py-1 bg-blue-50 italic text-gray-700 mb-2 text-sm">{children}</blockquote>,
      table: ({ children }: any) => <table className="w-full border-collapse border border-gray-300 mb-2 text-xs">{children}</table>,
      th: ({ children }: any) => <th className="border border-gray-300 px-2 py-1 bg-gray-50 font-semibold text-left">{children}</th>,
      td: ({ children }: any) => <td className="border border-gray-300 px-2 py-1">{children}</td>,
    }
  }
}

export default function MarkdownRenderer({ 
  content, 
  variant = 'default', 
  className 
}: MarkdownRendererProps) {
  if (!content || typeof content !== 'string') {
    return null
  }

  const variantConfig = variants[variant]

  return (
    <div className={cn(variantConfig.container, className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={variantConfig.components}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}

// Export specialized variants as separate components for convenience
export function AnalysisMarkdown({ content, className }: { content: string; className?: string }) {
  return <MarkdownRenderer content={content} variant="analysis" className={className} />
}

export function CompactMarkdown({ content, className }: { content: string; className?: string }) {
  return <MarkdownRenderer content={content} variant="compact" className={className} />
}