
'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface ProcessingStatusModalProps {
  isOpen: boolean;
}

const analysisMessages = [
  "🔍 Analyzing video content and extracting key frames...",
  "🎯 Identifying target audience signals and demographics...",
  "📊 Processing brand positioning and competitive landscape...",
  "💡 Evaluating creative strategy and core messaging...",
  "🎭 Assessing emotional resonance and cultural hooks...",
  "📈 Analyzing business objective alignment...",
  "⚡ Scoring memorability and brand distinctiveness...",
  "🔄 Cross-referencing competitive positioning data...",
  "📝 Generating strategic insights and recommendations...",
  "✨ Finalizing comprehensive marketing analysis..."
]

const ProcessingStatusModal = ({ isOpen }: ProcessingStatusModalProps) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0)
  const [progress, setProgress] = useState(10)

  useEffect(() => {
    if (!isOpen) {
      setCurrentMessageIndex(0)
      setProgress(10)
      return
    }

    const messageInterval = setInterval(() => {
      setCurrentMessageIndex(prev => {
        if (prev < analysisMessages.length - 1) {
          return prev + 1
        }
        return prev
      })
    }, 3000) // Change message every 3 seconds

    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev < 90) {
          return prev + Math.random() * 8 + 2 // Random increment between 2-10
        }
        return prev
      })
    }, 2000) // Update progress every 2 seconds

    return () => {
      clearInterval(messageInterval)
      clearInterval(progressInterval)
    }
  }, [isOpen])

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-lg">
        <CardHeader>
          <CardTitle className="flex items-center justify-center gap-2">
            <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
            AI Marketing Analysis in Progress
          </CardTitle>
          <CardDescription>Analyzing video ad strategy, creative execution, and market positioning</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4 overflow-hidden">
            <div 
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-1000 ease-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <div className="bg-gray-900 text-white font-mono text-xs p-4 rounded-md">
            <div className="flex items-center gap-2">
              <div className="animate-pulse">●</div>
              <span>{analysisMessages[currentMessageIndex]}</span>
            </div>
            <div className="mt-2 text-gray-400">
              Step {currentMessageIndex + 1} of {analysisMessages.length}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ProcessingStatusModal
