'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { BookText, CheckCircle, Search, Globe, ExternalLink } from 'lucide-react'

interface Citation {
  url: string;
  title: string;
  publication_date?: string;
  validated?: boolean;
  last_checked?: string;
  reliability_score?: number;
  source_type?: string;
}

interface CitationsCardProps {
  citations: Citation[];
}

const CitationsCard: React.FC<CitationsCardProps> = ({ citations }) => {
  if (!citations || citations.length === 0) {
    return null
  }

  // Only show valid citations (invalid ones are already filtered out by the API)
  const validCitations = citations.filter(citation => citation.validated !== false)

  if (validCitations.length === 0) {
    return null
  }

  const getSourceIcon = (sourceType: string) => {
    switch (sourceType) {
      case 'grounded_source':
        return <ExternalLink className="w-3 h-3 text-green-600" />
      case 'search_query':
        return <Search className="w-3 h-3 text-blue-600" />
      case 'validation_search':
        return <Search className="w-3 h-3 text-blue-600" />
      default:
        return <Globe className="w-3 h-3 text-gray-600" />
    }
  }

  const getReliabilityColor = (score: number) => {
    if (score >= 8) return 'bg-green-100 text-green-800'
    if (score >= 6) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-base">
          <BookText className="w-5 h-5 mr-2 text-gray-600" />
          Related Links
          {validCitations.some(c => c.validated) && (
            <CheckCircle className="w-4 h-4 ml-2 text-green-600" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2">
          {validCitations.map((citation, index) => (
            <li key={index} className="space-y-1">
              <div className="flex items-start gap-2">
                <div className="flex-shrink-0 mt-0.5">
                  {getSourceIcon(citation.source_type || 'web')}
                </div>
                <div className="flex-1 min-w-0">
                  <a
                    href={citation.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline text-xs block truncate"
                    title={citation.title}
                  >
                    {citation.title || citation.url}
                  </a>
                  <div className="flex items-center gap-2 mt-1">
                    {citation.validated && (
                      <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                        ✓ Validated
                      </Badge>
                    )}
                    {citation.reliability_score && (
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getReliabilityColor(citation.reliability_score)}`}
                      >
                        {citation.reliability_score}/10
                      </Badge>
                    )}
                    {citation.source_type === 'grounded_source' && (
                      <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                        AI Source
                      </Badge>
                    )}
                    {(citation.source_type === 'search_query' || citation.source_type === 'validation_search') && (
                      <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                        Search Query
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}

export default CitationsCard
