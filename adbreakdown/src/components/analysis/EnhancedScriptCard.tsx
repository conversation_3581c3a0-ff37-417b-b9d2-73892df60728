'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { BarChart3, BookOpen, Lightbulb, Mic, Film, Target, Zap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import Markdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

interface EnhancedScriptCardProps {
  enhancedScript: string
}

interface Scene {
  timecode: string
  duration: string
  title: string
  visualDescription: string
  characterAnalysis: string
  dialogueTranscription: string
  audioLandscape: string
  technicalCinematography: string
  sentimentAnalysis: {
    primarySentiment: string
    justification: string
    emotionalImpact: string
  }
  marketingAnalysis: {
    scenePurpose: string
    contributionToOverallMessage: string
    optimizationOpportunities: string
  }
  technicalProductionNotes: string
}

interface ParsedEnhancedScript {
  executiveSummary: {
    adConcept: string
    overallSentimentTrajectory: string
    duration: string
    primaryTargetAudience: string
  }
  sceneBySceneBreakdown: Scene[]
  finalAnalysis: {
    creativeInsights: string
    brandMessageAnalysis: string
    overallEffectivenessScore: string
    justification: string
    actionableRecommendations: string[]
  }
}

const parseEnhancedScript = (script: string): ParsedEnhancedScript | null => {
  try {
    // Attempt to parse as JSON first
    const jsonMatch = script.match(/```json\n([\s\S]*?)\n```/)
    if (jsonMatch && jsonMatch[1]) {
      return JSON.parse(jsonMatch[1])
    }

    // Fallback to regex parsing if not a JSON block
    const result: ParsedEnhancedScript = {
      executiveSummary: {
        adConcept: '',
        overallSentimentTrajectory: '',
        duration: '',
        primaryTargetAudience: '',
      },
      sceneBySceneBreakdown: [],
      finalAnalysis: {
        creativeInsights: '',
        brandMessageAnalysis: '',
        overallEffectivenessScore: '',
        justification: '',
        actionableRecommendations: [],
      },
    }

    // Executive Summary
    const esMatch = script.match(/## \*\* Executive Summary\*\*\n([\s\S]*?)(?=\n---\n## \*\* Scene-by-Scene Breakdown\*\*)/)
    if (esMatch) {
      const esContent = esMatch[1]
      result.executiveSummary.adConcept = esContent.match(/\*\*Ad Concept:\*\*\s*([\s\S]*?)(?=\n\*\*Overall Sentiment Trajectory:)/)?.[1]?.trim() || ''
      result.executiveSummary.overallSentimentTrajectory = esContent.match(/\*\*Overall Sentiment Trajectory:\*\*\s*([\s\S]*?)(?=\n\*\*Duration:)/)?.[1]?.trim() || ''
      result.executiveSummary.duration = esContent.match(/\*\*Duration:\*\*\s*([\s\S]*?)(?=\n\*\*Primary Target Audience:)/)?.[1]?.trim() || ''
      result.executiveSummary.primaryTargetAudience = esContent.match(/\*\*Primary Target Audience:\*\*\s*([\s\S]*)/)?.[1]?.trim() || ''
    }

    // Scene-by-Scene Breakdown
    const scenesMatches = script.matchAll(/### \*\*Scene (\d+): ([^\*]+?)\*\*\n\*\*⏱️ Timecode:\*\* ([^\*]+?) \*\*\(Duration: ([^\)]+?)\*\*\n([\s\S]*?)(?=(?:### \*\*Scene \d+)|(?:\n---\n## \*\*Final Analysis\*\*))/g)
    for (const match of scenesMatches) {
      const sceneContent = match[5]
      const scene: Scene = {
        timecode: match[3].trim(),
        duration: match[4].trim(),
        title: match[2].trim(),
        visualDescription: sceneContent.match(/#### \*\* Visual Description\*\*\n([\s\S]*?)(?=\n#### \*\* Character Analysis & Movement)/)?.[1]?.trim() || '',
        characterAnalysis: sceneContent.match(/#### \*\* Character Analysis & Movement\*\*\n([\s\S]*?)(?=\n#### \*\*️ Dialogue Transcription)/)?.[1]?.trim() || '',
        dialogueTranscription: sceneContent.match(/#### \*\*️ Dialogue Transcription\*\*\n([\s\S]*?)(?=\n#### \*\* Audio Landscape)/)?.[1]?.trim() || '',
        audioLandscape: sceneContent.match(/#### \*\* Audio Landscape\*\*\n([\s\S]*?)(?=\n#### \*\* Technical Cinematography)/)?.[1]?.trim() || '',
        technicalCinematography: sceneContent.match(/#### \*\* Technical Cinematography\*\*\n([\s\S]*?)(?=\n#### \*\* Sentiment Analysis)/)?.[1]?.trim() || '',
        sentimentAnalysis: {
          primarySentiment: sceneContent.match(/#### \*\* Sentiment Analysis\*\*\n\*\*Primary Sentiment:\*\* ([^\*]+?)\*\*\n\*\*Justification:\*\* ([\s\S]*?)(?=\n\*\*Emotional Impact:)/)?.[1]?.trim() || '',
          justification: sceneContent.match(/#### \*\* Sentiment Analysis\*\*\n\*\*Primary Sentiment:\*\* ([^\*]+?)\*\*\n\*\*Justification:\*\* ([\s\S]*?)(?=\n\*\*Emotional Impact:)/)?.[2]?.trim() || '',
          emotionalImpact: sceneContent.match(/\*\*Emotional Impact:\*\*\s*([\s\S]*?)(?=\n#### \*\* Marketing Analysis)/)?.[1]?.trim() || '',
        },
        marketingAnalysis: {
          scenePurpose: sceneContent.match(/#### \*\* Marketing Analysis\*\*\n\*\*Scene Purpose:\*\* ([^\*]+?)\*\*\n\*\*Contribution to Overall Message:\*\* ([^\*]+?)\*\*\n\*\*Optimization Opportunities:\*\* ([\s\S]*?)(?=\n#### \*\*⚙️ Technical Production Notes)/)?.[1]?.trim() || '',
          contributionToOverallMessage: sceneContent.match(/#### \*\* Marketing Analysis\*\*\n\*\*Scene Purpose:\*\* ([^\*]+?)\*\*\n\*\*Contribution to Overall Message:\*\* ([^\*]+?)\*\*\n\*\*Optimization Opportunities:\*\* ([\s\S]*?)(?=\n#### \*\*⚙️ Technical Production Notes)/)?.[2]?.trim() || '',
          optimizationOpportunities: sceneContent.match(/#### \*\* Marketing Analysis\*\*\n\*\*Scene Purpose:\*\* ([^\*]+?)\*\*\n\*\*Contribution to Overall Message:\*\* ([^\*]+?)\*\*\n\*\*Optimization Opportunities:\*\* ([\s\S]*?)(?=\n#### \*\*⚙️ Technical Production Notes)/)?.[3]?.trim() || '',
        },
        technicalProductionNotes: sceneContent.match(/#### \*\*⚙️ Technical Production Notes\*\*\n([\s\S]*)/)?.[1]?.trim() || '',
      }
      result.sceneBySceneBreakdown.push(scene)
    }

    // Final Analysis
    const faMatch = script.match(/## \*\*Final Analysis\*\*\n([\s\S]*)/)
    if (faMatch) {
      const faContent = faMatch[1]
      result.finalAnalysis.creativeInsights = faContent.match(/\*\* Creative Insights:\*\*\n([\s\S]*?)(?=\n\*\*️ Brand Message Analysis:)/)?.[1]?.trim() || ''
      result.finalAnalysis.brandMessageAnalysis = faContent.match(/\*\*️ Brand Message Analysis:\*\*\n([\s\S]*?)(?=\n\*\* Overall Effectiveness Score:)/)?.[1]?.trim() || ''
      result.finalAnalysis.overallEffectivenessScore = faContent.match(/\*\* Overall Effectiveness Score:\*\*\s*([^\n]+)/)?.[1]?.trim() || ''
      result.finalAnalysis.justification = faContent.match(/\*\*Justification:\*\*\s*([\s\S]*?)(?=\n\*\* Actionable Recommendations:)/)?.[1]?.trim() || ''
      const recommendationsMatch = faContent.match(/\*\* Actionable Recommendations:\*\*\n([\s\S]*)/)?.[1]
      if (recommendationsMatch) {
        result.finalAnalysis.actionableRecommendations = recommendationsMatch.split('\n').map(line => line.replace(/^- /, '').trim()).filter(line => line.length > 0)
      }
    }

    return result
  } catch (error) {
    console.error("Error parsing enhanced script:", error)
    return null
  }
}

const EnhancedScriptCard: React.FC<EnhancedScriptCardProps> = ({ enhancedScript }) => {
  const parsedScript = parseEnhancedScript(enhancedScript)
  const [selectedSceneIndex, setSelectedSceneIndex] = useState(0)
  const [viewMode, setViewMode] = useState<'scene-first' | 'dimension-first'>('scene-first')
  const [selectedDimension, setSelectedDimension] = useState<string>('visuals')

  if (!enhancedScript || !parsedScript) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg md:text-xl flex items-center">
            <BookOpen className="w-5 h-5 mr-2 text-gray-600" /> Enhanced Script Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">No enhanced script analysis available.</p>
        </CardContent>
      </Card>
    )
  }

  const currentScene = parsedScript.sceneBySceneBreakdown[selectedSceneIndex]

  const renderMarkdown = (content: string) => (
    <Markdown remarkPlugins={[remarkGfm]} className="prose prose-sm max-w-none text-gray-700">
      {content}
    </Markdown>
  )

  const renderDimensionContent = (dimension: string, scene: Scene) => {
    switch (dimension) {
      case 'visuals':
        return renderMarkdown(scene.visualDescription)
      case 'characters_dialogue':
        return (
          <>
            <h4 className="font-semibold text-md mb-2">Character Analysis & Movement</h4>
            {renderMarkdown(scene.characterAnalysis)}
            <h4 className="font-semibold text-md mt-4 mb-2">Dialogue Transcription</h4>
            {renderMarkdown(scene.dialogueTranscription)}
          </>
        )
      case 'audio_cinematography':
        return (
          <>
            <h4 className="font-semibold text-md mb-2">Audio Landscape</h4>
            {renderMarkdown(scene.audioLandscape)}
            <h4 className="font-semibold text-md mt-4 mb-2">Technical Cinematography</h4>
            {renderMarkdown(scene.technicalCinematography)}
          </>
        )
      case 'sentiment':
        return (
          <>
            <h4 className="font-semibold text-md mb-2">Primary Sentiment</h4>
            <p className="text-gray-700">{scene.sentimentAnalysis.primarySentiment}</p>
            <h4 className="font-semibold text-md mt-4 mb-2">Justification</h4>
            {renderMarkdown(scene.sentimentAnalysis.justification)}
            <h4 className="font-semibold text-md mt-4 mb-2">Emotional Impact</h4>
            {renderMarkdown(scene.sentimentAnalysis.emotionalImpact)}
          </>
        )
      case 'marketing':
        return (
          <>
            <h4 className="font-semibold text-md mb-2">Scene Purpose</h4>
            {renderMarkdown(scene.marketingAnalysis.scenePurpose)}
            <h4 className="font-semibold text-md mt-4 mb-2">Contribution to Overall Message</h4>
            {renderMarkdown(scene.marketingAnalysis.contributionToOverallMessage)}
            <h4 className="font-semibold text-md mt-4 mb-2">Optimization Opportunities</h4>
            {renderMarkdown(scene.marketingAnalysis.optimizationOpportunities)}
          </>
        )
      case 'production_notes':
        return renderMarkdown(scene.technicalProductionNotes)
      default:
        return null
    }
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg md:text-xl flex items-center">
          <BookOpen className="w-5 h-5 mr-2 text-gray-600" /> Enhanced Script Analysis
        </CardTitle>
        <p className="text-sm text-gray-500">A detailed, scene-by-scene breakdown of the ad&apos;s creative and technical elements.</p>
      </CardHeader>
      <CardContent>
        {/* Executive Summary */}
        <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
          <h3 className="text-md font-semibold mb-3 flex items-center"><Lightbulb className="w-4 h-4 mr-2" /> Executive Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p><strong className="text-gray-700">Ad Concept:</strong> {renderMarkdown(parsedScript.executiveSummary.adConcept)}</p>
              <p className="mt-2"><strong className="text-gray-700">Overall Sentiment Trajectory:</strong> {parsedScript.executiveSummary.overallSentimentTrajectory}</p>
            </div>
            <div>
              <p><strong className="text-gray-700">Duration:</strong> {parsedScript.executiveSummary.duration}</p>
              <p className="mt-2"><strong className="text-gray-700">Primary Target Audience:</strong> {parsedScript.executiveSummary.primaryTargetAudience}</p>
            </div>
          </div>
        </div>

        <Separator className="my-6" />

        {/* Scene-by-Scene Breakdown */}
        <h3 className="text-md font-semibold mb-4 flex items-center"><Film className="w-4 h-4 mr-2" /> Scene-by-Scene Breakdown</h3>
        <div className="flex justify-end mb-4">
          <Button
            variant={viewMode === 'scene-first' ? 'default' : 'outline'}
            onClick={() => setViewMode('scene-first')}
            size="sm"
            className="mr-2"
          >
            Scene-First View
          </Button>
          <Button
            variant={viewMode === 'dimension-first' ? 'default' : 'outline'}
            onClick={() => setViewMode('dimension-first')}
            size="sm"
          >
            Dimension-First View
          </Button>
        </div>

        {viewMode === 'scene-first' ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Pane: Scene Navigator */}
            <div className="lg:col-span-1 border-r pr-4">
              <h4 className="font-semibold text-sm mb-3">Scenes</h4>
              <div className="space-y-2">
                {parsedScript.sceneBySceneBreakdown.map((scene, index) => (
                  <Button
                    key={index}
                    variant={selectedSceneIndex === index ? 'secondary' : 'ghost'}
                    onClick={() => setSelectedSceneIndex(index)}
                    className="w-full justify-start h-auto py-2 px-3 text-left"
                  >
                    <span className="block font-medium">Scene {index + 1}: {scene.title}</span>
                    <span className="block text-xs text-gray-500">{scene.timecode}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Right Pane: Multi-Dimensional Analysis Tabs */}
            <div className="lg:col-span-2">
              {currentScene && (
                <>
                  <h4 className="text-lg font-semibold mb-3">Scene {selectedSceneIndex + 1}: {currentScene.title}</h4>
                  <p className="text-sm text-gray-500 mb-4">Timecode: {currentScene.timecode} (Duration: {currentScene.duration})</p>
                  <Tabs defaultValue="visuals" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-6">
                      <TabsTrigger value="visuals">Visuals</TabsTrigger>
                      <TabsTrigger value="characters_dialogue">Characters & Dialogue</TabsTrigger>
                      <TabsTrigger value="audio_cinematography">Audio & Cinematography</TabsTrigger>
                      <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
                      <TabsTrigger value="marketing">Marketing</TabsTrigger>
                      <TabsTrigger value="production_notes">Production Notes</TabsTrigger>
                    </TabsList>
                    <TabsContent value="visuals" className="mt-4">{renderDimensionContent('visuals', currentScene)}</TabsContent>
                    <TabsContent value="characters_dialogue" className="mt-4">{renderDimensionContent('characters_dialogue', currentScene)}</TabsContent>
                    <TabsContent value="audio_cinematography" className="mt-4">{renderDimensionContent('audio_cinematography', currentScene)}</TabsContent>
                    <TabsContent value="sentiment" className="mt-4">{renderDimensionContent('sentiment', currentScene)}</TabsContent>
                    <TabsContent value="marketing" className="mt-4">{renderDimensionContent('marketing', currentScene)}</TabsContent>
                    <TabsContent value="production_notes" className="mt-4">{renderDimensionContent('production_notes', currentScene)}</TabsContent>
                  </Tabs>
                </>
              )}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Pane: Dimension Selector */}
            <div className="lg:col-span-1 border-r pr-4">
              <h4 className="font-semibold text-sm mb-3">Dimensions</h4>
              <div className="space-y-2">
                <Button
                  variant={selectedDimension === 'visuals' ? 'secondary' : 'ghost'}
                  onClick={() => setSelectedDimension('visuals')}
                  className="w-full justify-start h-auto py-2 px-3 text-left"
                >
                  <span className="block font-medium">Visuals</span>
                </Button>
                <Button
                  variant={selectedDimension === 'characters_dialogue' ? 'secondary' : 'ghost'}
                  onClick={() => setSelectedDimension('characters_dialogue')}
                  className="w-full justify-start h-auto py-2 px-3 text-left"
                >
                  <span className="block font-medium">Characters & Dialogue</span>
                </Button>
                <Button
                  variant={selectedDimension === 'audio_cinematography' ? 'secondary' : 'ghost'}
                  onClick={() => setSelectedDimension('audio_cinematography')}
                  className="w-full justify-start h-auto py-2 px-3 text-left"
                >
                  <span className="block font-medium">Audio & Cinematography</span>
                </Button>
                <Button
                  variant={selectedDimension === 'sentiment' ? 'secondary' : 'ghost'}
                  onClick={() => setSelectedDimension('sentiment')}
                  className="w-full justify-start h-auto py-2 px-3 text-left"
                >
                  <span className="block font-medium">Sentiment</span>
                </Button>
                <Button
                  variant={selectedDimension === 'marketing' ? 'secondary' : 'ghost'}
                  onClick={() => setSelectedDimension('marketing')}
                  className="w-full justify-start h-auto py-2 px-3 text-left"
                >
                  <span className="block font-medium">Marketing</span>
                </Button>
                <Button
                  variant={selectedDimension === 'production_notes' ? 'secondary' : 'ghost'}
                  onClick={() => setSelectedDimension('production_notes')}
                  className="w-full justify-start h-auto py-2 px-3 text-left"
                >
                  <span className="block font-medium">Production Notes</span>
                </Button>
              </div>
            </div>

            {/* Right Pane: Content Flow */}
            <div className="lg:col-span-2">
              <h4 className="text-lg font-semibold mb-3">
                {selectedDimension.replace(/_/g, ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} Flow
              </h4>
              <div className="space-y-6">
                {parsedScript.sceneBySceneBreakdown.map((scene, index) => (
                  <div key={index} className="border p-4 rounded-md bg-gray-50">
                    <h5 className="font-semibold text-md mb-2">Scene {index + 1}: {scene.title} ({scene.timecode})</h5>
                    {renderDimensionContent(selectedDimension, scene)}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        <Separator className="my-6" />

        {/* Final Analysis */}
        <div className="mb-6">
          <h3 className="text-md font-semibold mb-3 flex items-center"><BarChart3 className="w-4 h-4 mr-2" /> Final Analysis</h3>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-semibold text-md mb-1">Creative Insights</h4>
              {renderMarkdown(parsedScript.finalAnalysis.creativeInsights)}
            </div>
            <div>
              <h4 className="font-semibold text-md mb-1">Brand Message Analysis</h4>
              {renderMarkdown(parsedScript.finalAnalysis.brandMessageAnalysis)}
            </div>
            <div>
              <h4 className="font-semibold text-md mb-1">Overall Effectiveness Score</h4>
              <p className="text-gray-700">{parsedScript.finalAnalysis.overallEffectivenessScore}</p>
              <h4 className="font-semibold text-md mt-2 mb-1">Justification</h4>
              {renderMarkdown(parsedScript.finalAnalysis.justification)}
            </div>
            <div>
              <h4 className="font-semibold text-md mb-1">Actionable Recommendations</h4>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                {parsedScript.finalAnalysis.actionableRecommendations.map((rec, index) => (
                  <li key={index}>{renderMarkdown(rec)}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default EnhancedScriptCard
