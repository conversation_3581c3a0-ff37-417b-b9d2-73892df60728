'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  CheckCircle, 
  AlertTriangle, 
  Info,
  ExternalLink
} from 'lucide-react'
import { useMetadataValidation } from '@/hooks/useMetadataValidation'

interface MetadataValidationCardProps {
  analysisId: string;
  currentMetadata?: any;
  existingValidation?: any; // llm_correction data from database
}

const MetadataValidationCard = ({ analysisId, currentMetadata, existingValidation }: MetadataValidationCardProps) => {
  const {
    isValidating,
    validationResult,
    error,
    validateMetadata,
    resetValidation,
    getSignificantDiscrepancies,
    getHighPriorityRecommendations,
    getLowConfidenceFields
  } = useMetadataValidation();

  // Use existing validation data if available and no new validation is in progress
  const displayValidation = validationResult || (existingValidation?.type === 'metadata_validation' ? {
    validation_result: existingValidation.validation_result,
    search_grounding_used: existingValidation.search_grounding_used,
    original_metadata: existingValidation.original_metadata
  } : null);

  // Helper to get timestamp of existing validation
  const getValidationTimestamp = () => {
    if (validationResult) return 'Just now';
    if (existingValidation?.timestamp) {
      const date = new Date(existingValidation.timestamp);
      const now = new Date();
      const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      
      if (diffMinutes < 60) return `${diffMinutes} minutes ago`;
      if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)} hours ago`;
      return `${Math.floor(diffMinutes / 1440)} days ago`;
    }
    return null;
  };

  // Custom helper functions that work with displayValidation (existing or new)
  const getDisplaySignificantDiscrepancies = () => {
    if (!displayValidation) return [];
    
    return displayValidation.validation_result.discrepancy_report?.filter(
      (discrepancy: any) => discrepancy.impact_level === 'high' || discrepancy.impact_level === 'medium'
    ) || [];
  };

  const getDisplayHighPriorityRecommendations = () => {
    if (!displayValidation) return [];
    
    return displayValidation.validation_result.enhancement_recommendations?.filter(
      (rec: any) => rec.priority === 'high'
    ) || [];
  };

  const getDisplayLowConfidenceFields = () => {
    if (!displayValidation) return [];
    
    const metadata = displayValidation.validation_result.validated_metadata;
    if (!metadata) return [];
    
    const lowConfidenceFields = [];
    
    for (const [fieldName, fieldData] of Object.entries(metadata)) {
      if ((fieldData as any)?.confidence < 6) {
        lowConfidenceFields.push({
          field: fieldName,
          confidence: (fieldData as any).confidence,
          value: (fieldData as any).value
        });
      }
    }
    
    return lowConfidenceFields;
  };

  const handleValidate = async () => {
    try {
      await validateMetadata(analysisId);
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 8) return 'bg-green-100 text-green-800 border-green-200';
    if (confidence >= 6) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Metadata Validation
        </CardTitle>
        <CardDescription>
          Validate and enhance marketing analysis metadata using real-time web search data.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!displayValidation && !error && (
          <div className="space-y-4">
            {currentMetadata && (
              <div>
                <h4 className="text-sm font-medium mb-2">Current Metadata:</h4>
                <div className="grid grid-cols-1 gap-1 text-xs">
                  {currentMetadata.ad_title && (
                    <div><strong>Title:</strong> {currentMetadata.ad_title}</div>
                  )}
                  {currentMetadata.brand && (
                    <div><strong>Brand:</strong> {currentMetadata.brand}</div>
                  )}
                  {currentMetadata.product_category && (
                    <div><strong>Category:</strong> {currentMetadata.product_category}</div>
                  )}
                  {currentMetadata.parent_entity && (
                    <div><strong>Parent:</strong> {currentMetadata.parent_entity}</div>
                  )}
                  {currentMetadata.campaign_category && (
                    <div><strong>Campaign:</strong> {currentMetadata.campaign_category}</div>
                  )}
                  {currentMetadata.celebrity && (
                    <div><strong>Celebrity:</strong> {currentMetadata.celebrity}</div>
                  )}
                  {currentMetadata.geography && (
                    <div><strong>Geography:</strong> {currentMetadata.geography}</div>
                  )}
                  {currentMetadata.agency && (
                    <div><strong>Agency:</strong> {currentMetadata.agency}</div>
                  )}
                  {currentMetadata.runtime && (
                    <div><strong>Runtime:</strong> {currentMetadata.runtime}</div>
                  )}
                  {currentMetadata.director && (
                    <div><strong>Director:</strong> {currentMetadata.director}</div>
                  )}
                  {currentMetadata.creative_team && (
                    <div><strong>Creative Team:</strong> {currentMetadata.creative_team}</div>
                  )}
                  {currentMetadata.production_company && (
                    <div><strong>Production:</strong> {currentMetadata.production_company}</div>
                  )}
                  {currentMetadata.music_composer && (
                    <div><strong>Music:</strong> {currentMetadata.music_composer}</div>
                  )}
                  {currentMetadata.sound_designer && (
                    <div><strong>Sound:</strong> {currentMetadata.sound_designer}</div>
                  )}
                </div>
              </div>
            )}
            
            <Button 
              onClick={handleValidate}
              disabled={isValidating}
              className="w-full"
            >
              {isValidating ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Validating Metadata...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Validate Metadata with Search
                </>
              )}
            </Button>
          </div>
        )}

        {error && (
          <div className="border border-red-200 bg-red-50 p-3 rounded-lg flex items-start gap-2">
            <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
            <div className="text-red-800 text-sm">
              {error}
            </div>
          </div>
        )}

        {displayValidation && (
          <div className="space-y-6">
            {/* Validation Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                Validation Summary
                {getValidationTimestamp() && (
                  <span className="text-xs text-gray-500 ml-auto">{getValidationTimestamp()}</span>
                )}
                {(displayValidation as any).search_grounding_used !== undefined && (
                  <Badge variant={(displayValidation as any).search_grounding_used ? "default" : "outline"} className="ml-2">
                    {(displayValidation as any).search_grounding_used ? "🔍 Search Used" : "📚 Knowledge Only"}
                  </Badge>
                )}
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Overall Accuracy:</span>
                  <Badge className={getConfidenceColor(displayValidation.validation_result.validation_summary.overall_accuracy_score)}>
                    {displayValidation.validation_result.validation_summary.overall_accuracy_score}/10
                  </Badge>
                </div>
                <div>
                  <span className="text-gray-600">Confidence:</span>
                  <Badge variant="outline">
                    {displayValidation.validation_result.validation_summary.confidence_level}
                  </Badge>
                </div>
                <div>
                  <span className="text-gray-600">Discrepancies:</span>
                  <span className="font-medium">
                    {displayValidation.validation_result.validation_summary.major_discrepancies_found}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Enhanced Fields:</span>
                  <span className="font-medium">
                    {displayValidation.validation_result.validation_summary.fields_enhanced}
                  </span>
                </div>
              </div>
            </div>

            {/* Significant Discrepancies */}
            {getDisplaySignificantDiscrepancies().length > 0 && (
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  Significant Discrepancies
                </h4>
                <div className="space-y-2">
                  {getDisplaySignificantDiscrepancies().map((discrepancy: any, index: number) => (
                    <div key={index} className="border rounded-lg p-3 bg-orange-50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium capitalize">{discrepancy.field}</span>
                        <Badge className={getImpactColor(discrepancy.impact_level)}>
                          {discrepancy.impact_level} impact
                        </Badge>
                      </div>
                      <div className="text-sm space-y-1">
                        <div><strong>Original:</strong> {discrepancy.original_value}</div>
                        <div><strong>Corrected:</strong> {discrepancy.corrected_value}</div>
                        <div><strong>Type:</strong> {discrepancy.discrepancy_type}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* High Priority Recommendations */}
            {getDisplayHighPriorityRecommendations().length > 0 && (
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  High Priority Recommendations
                </h4>
                <div className="space-y-2">
                  {getDisplayHighPriorityRecommendations().map((rec: any, index: number) => (
                    <div key={index} className="border rounded-lg p-3 bg-blue-50">
                      <div className="font-medium capitalize mb-1">{rec.field}</div>
                      <div className="text-sm text-gray-600 mb-1">{rec.recommendation}</div>
                      <div className="text-xs text-gray-500">{rec.reasoning}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Low Confidence Fields */}
            {getDisplayLowConfidenceFields().length > 0 && (
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  Low Confidence Fields & Alternative Searches
                </h4>
                <div className="space-y-3">
                  {getDisplayLowConfidenceFields().map((field, index) => {
                    const fieldData = displayValidation?.validation_result.validated_metadata[field.field];
                    const alternativeSearches = fieldData?.alternative_searches_performed;
                    
                    return (
                      <div key={index} className="border rounded-lg p-3 bg-yellow-50">
                        <div className="flex items-center justify-between mb-2">
                          <span className="capitalize font-medium">{field.field}</span>
                          <Badge className={getConfidenceColor(field.confidence)}>
                            {field.confidence}/10
                          </Badge>
                        </div>
                        {field.value && (
                          <div className="text-sm text-gray-700 mb-1">
                            <strong>Current Value:</strong> {field.value}
                          </div>
                        )}
                        {alternativeSearches && (
                          <div className="text-xs text-gray-600 mt-2">
                            <strong>Alternative Searches Performed:</strong>
                            <div className="mt-1 italic">{alternativeSearches}</div>
                          </div>
                        )}
                        {fieldData?.source_verification && (
                          <div className="text-xs text-gray-500 mt-1">
                            <strong>Sources:</strong> {fieldData.source_verification}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Search Sources */}
            {displayValidation.validation_result.search_sources?.length > 0 && (
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <ExternalLink className="h-4 w-4 text-gray-600" />
                  Search Sources Used
                </h4>
                <div className="space-y-2">
                  {displayValidation.validation_result.search_sources.map((source: any, index: number) => (
                    <div key={index} className="border rounded-lg p-3 bg-gray-50">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">&ldquo;{source.query_used}&rdquo;</span>
                        <Badge className={getConfidenceColor(source.reliability_score)}>
                          {source.reliability_score}/10
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-600">{source.key_findings}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <Button 
                onClick={resetValidation}
                variant="outline"
                size="sm"
              >
                Reset
              </Button>
              <Button 
                onClick={handleValidate}
                disabled={isValidating}
                size="sm"
              >
                Re-validate
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MetadataValidationCard;