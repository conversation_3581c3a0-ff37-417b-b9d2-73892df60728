'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import MarkdownRenderer from '@/components/ui/markdown-renderer'

interface GeneratedContentSectionsProps {
  marketingCopy?: string
  socialMediaPosts?: string
  marketingScorecard?: string
  seoKeywords?: string
  contentSuggestions?: string
  }

export default function GeneratedContentSections({
  marketingCopy,
  socialMediaPosts,
  marketingScorecard,
  seoKeywords,
  contentSuggestions
}: GeneratedContentSectionsProps) {
  // Helper function to render HTML content (for legacy content)
  const renderHtmlContent = (content: string) => (
    <div className="generated-content" dangerouslySetInnerHTML={{ __html: content.replace(/\n/g, '<br />') }} />
  )

  // Helper function to render markdown content
  const renderMarkdownContent = (content: string) => (
    <MarkdownRenderer 
      content={content} 
      variant="default"
      className="generated-content table-container"
    />
  )

  return (
    <div className="mt-8 space-y-8">
      {marketingCopy && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Marketing Copy</CardTitle>
          </CardHeader>
          <CardContent>
            {renderHtmlContent(marketingCopy)}
          </CardContent>
        </Card>
      )}
      
      {socialMediaPosts && (
        <Card>
          <CardHeader>
            <CardTitle>Proposed Social Media Posts</CardTitle>
          </CardHeader>
          <CardContent>
            {renderHtmlContent(socialMediaPosts)}
          </CardContent>
        </Card>
      )}
      
      {marketingScorecard && (
        <Card>
          <CardHeader>
            <CardTitle>Marketing Scorecard</CardTitle>
          </CardHeader>
          <CardContent>
            {renderMarkdownContent(marketingScorecard)}
          </CardContent>
        </Card>
      )}
      
      {seoKeywords && (
        <Card>
          <CardHeader>
            <CardTitle>Extracted SEO Keywords</CardTitle>
          </CardHeader>
          <CardContent>
            {renderHtmlContent(seoKeywords)}
          </CardContent>
        </Card>
      )}
      
      {contentSuggestions && (
        <Card>
          <CardHeader>
            <CardTitle>Content Improvement Suggestions</CardTitle>
          </CardHeader>
          <CardContent>
            {renderHtmlContent(contentSuggestions)}
          </CardContent>
        </Card>
      )}
      
    </div>
  )
}