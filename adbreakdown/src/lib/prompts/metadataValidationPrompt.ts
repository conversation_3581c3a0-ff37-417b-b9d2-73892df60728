/**
 * Metadata Validation Prompt
 * 
 * This prompt takes a YouTube URL and existing marketing analysis metadata,
 * then uses search grounding to validate and enhance the metadata accuracy.
 */

export function getMetadataValidationPrompt(youtubeUrl: string, currentMetadata: any): string {
  const metadataFields = Object.keys(currentMetadata)
  const fieldList = metadataFields.map(field => `- ${field}`).join('\n')
  
  return `You are an expert marketing analyst with access to real-time search data. Your task is to validate and enhance marketing analysis metadata using current, factual information from the web.

## TASK OVERVIEW
You have been provided with:
1. A YouTube video URL: ${youtubeUrl}
2. Existing metadata extracted from AI analysis

Your job is to:
- Verify the accuracy of the extracted metadata using web search
- Correct any inaccuracies found
- Enhance missing or incomplete information
- Provide confidence scores for each metadata field
- Flag any discrepancies between the AI analysis and factual data

## INPUT METADATA TO VALIDATE
${JSON.stringify(currentMetadata, null, 2)}

## DETECTED METADATA FIELDS
The following metadata fields have been identified for validation:
${fieldList}

## VALIDATION INSTRUCTIONS

For each of the detected metadata fields, perform the following validations as applicable:

### GENERAL VALIDATION APPROACH
1. **Field-Specific Validation**: Tailor validation strategy based on the field type
2. **Cross-Reference**: Use multiple sources to verify accuracy
3. **Context Awareness**: Consider the relationship between fields
4. **Confidence Scoring**: Provide honest assessment of data reliability

### FIELD-SPECIFIC VALIDATION STRATEGIES

${metadataFields.map(field => {
  switch (field.toLowerCase()) {
    case 'brand':
    case 'ad_title':
      return `#### ${field.toUpperCase()} VERIFICATION
- Search for the brand name to verify correct spelling and official name
- Check if the brand is part of a larger parent company
- Verify the brand's primary business/industry
- Look for recent brand campaigns or rebranding efforts`
    
    case 'product_category':
      return `#### ${field.toUpperCase()} VALIDATION
- Confirm the product category is accurate and specific
- Check industry standard categorizations
- Verify if this is the primary product line for the brand`
    
    case 'campaign_category':
      return `#### ${field.toUpperCase()} ASSESSMENT
- Research recent marketing campaigns from this brand
- Analyze campaign objectives based on brand's current market position
- Verify if this fits the brand's typical campaign strategy`
    
    case 'celebrity':
    case 'talent':
      return `#### ${field.toUpperCase()} VERIFICATION
- If celebrities are mentioned, verify their correct names and spelling
- Check if they have official brand partnerships or endorsement deals
- Confirm their relevance to the brand's target demographic`
    
    case 'geography':
    case 'market':
      return `#### ${field.toUpperCase()} VALIDATION
- Verify the market/geography where this campaign was launched
- Check if the brand operates in the suggested geography
- Look for region-specific campaign adaptations`
    
    case 'agency':
    case 'creative_agency':
      return `#### ${field.toUpperCase()} VALIDATION
- Search for the advertising agency behind the campaign
- If agency is unknown or confidence < 5, search for alternatives:
  * "[brand name] advertising agency [year]"
  * "[brand name] creative agency partnership"
  * "[brand name] marketing campaign credits"
- Verify production company credentials
- Check for award recognitions or industry mentions`
    
    case 'runtime':
    case 'duration':
      return `#### ${field.toUpperCase()} VERIFICATION
- Verify the video duration matches the reported runtime
- Check for any discrepancies in timing
- Consider if this is a standard ad format duration`
    
    case 'parent_entity':
    case 'parent_company':
      return `#### ${field.toUpperCase()} VALIDATION
- Search for parent company information
- Verify ownership structure and subsidiaries
- Check for recent acquisitions or corporate changes`
    
    default:
      return `#### ${field.toUpperCase()} VALIDATION
- Research and verify the accuracy of this field
- Look for authoritative sources that can confirm or contradict the value
- Consider the context within the overall campaign analysis`
  }
}).join('\n\n')}

### TEMPORAL CONTEXT
- Check when the campaign was launched
- Verify if the metadata aligns with the brand's timeline
- Look for seasonal or event-based campaign context

## SEARCH STRATEGY
Use your web search capabilities to:
1. Search for "[brand name] advertisement" and "[brand name] marketing campaign"
2. Search for "[brand name] + [celebrity name] endorsement" (if celebrity mentioned)
3. Search for "[brand name] + [agency name]" to verify agency relationships
4. Search for "[brand name] parent company" or "[brand name] owned by"
5. Search for product category validation: "[brand name] + [product category]"
6. Search for geographic market information: "[brand name] [country] campaign"

**ALTERNATIVE SEARCH STRATEGIES for LOW-CONFIDENCE FIELDS (score < 5):**
If any field has confidence < 5, perform additional targeted searches:

For **Agency/Creative Team**:
- "[brand name] advertising agency 2024"
- "[brand name] creative agency partnership"
- "[celebrity name] [brand name] advertisement credits"
- "[brand name] marketing campaign agency behind"
- "who created [brand name] advertisement"

For **Director/Production**:
- "[brand name] advertisement director"
- "[celebrity name] [brand name] commercial director"
- "[brand name] production company credits"

For **Parent Company** (when unclear):
- "[brand name] parent company"
- "[brand name] owned by subsidiary"
- "[brand name] manufacturer company"

For **Celebrity** (when uncertain):
- "[brand name] brand ambassador"
- "[brand name] spokesperson endorsement"
- "[brand name] celebrity partnership"

EXAMPLE SEARCHES for Revital with alternatives:
- "Revital advertisement Salman Khan"
- "Revital multivitamin Ranbaxy"
- "Lowe Lintas Revital campaign"
- "Revital health supplement India"
- **If agency confidence < 5**: "Revital advertising agency 2024", "Salman Khan Revital commercial agency"

## OUTPUT FORMAT
Return a JSON object with the following structure:

{
  "validation_summary": {
    "overall_accuracy_score": "[0-10 scale]",
    "major_discrepancies_found": "[number]",
    "fields_enhanced": "[number]",
    "confidence_level": "[high/medium/low]"
  },
  "validated_metadata": {
${metadataFields.map(field => {
    const needsAlternativeSearches = ['celebrity', 'talent', 'agency', 'creative_agency'].includes(field.toLowerCase())
    const baseStructure = `    "${field}": {
      "value": "[corrected/confirmed value${field.toLowerCase().includes('celebrity') || field.toLowerCase().includes('agency') || field.toLowerCase().includes('parent') ? ' or null' : ''}]",
      "confidence": "[0-10]",
      "changes_made": "[description of any changes]",
      "source_verification": "[brief description of sources used]"${needsAlternativeSearches ? ',\n      "alternative_searches_performed": "[if confidence < 5, list additional searches performed]"' : ''}
    }`
    return baseStructure
  }).join(',\n')}
  },
  "enhancement_recommendations": [
    {
      "field": "[metadata field name]",
      "recommendation": "[specific recommendation]",
      "reasoning": "[why this enhancement is suggested]",
      "priority": "[high/medium/low]"
    }
  ],
  "discrepancy_report": [
    {
      "field": "[metadata field name]",
      "original_value": "[value from input]",
      "corrected_value": "[validated value]",
      "discrepancy_type": "[spelling/factual/missing/incorrect]",
      "impact_level": "[high/medium/low]"
    }
  ],
  "search_sources": [
    {
      "query_used": "[search query]",
      "key_findings": "[summary of findings]",
      "reliability_score": "[0-10]"
    }
  ]
}

## IMPORTANT GUIDELINES
1. **Accuracy First**: Prioritize factual accuracy over preserving original data
2. **Alternative Search Mandate**: If any field scores below 5 confidence, MUST perform alternative searches using the strategies above
3. **Transparency**: Clearly document all changes and sources
4. **Confidence Scoring**: Be honest about confidence levels (0-10 scale)
5. **Source Quality**: Prefer official brand websites, industry publications, and reputable news sources
6. **Recency**: Consider the temporal context of the information
7. **Specificity**: Provide specific, actionable corrections rather than vague suggestions
8. **Exhaustive Search**: For low-confidence fields, try multiple search variations before settling on "unknown". Use direct search like Ex:Which agency made the /title/ ad for /brand/ with /celebrity/?


## CONFIDENCE SCORING GUIDE
- **9-10**: Multiple reliable sources confirm the information
- **7-8**: Good sources support the information with minor uncertainty
- **5-6**: Some sources support but with notable gaps or conflicts
- **3-4**: Limited or conflicting information available
- **1-2**: Minimal or unreliable sources, high uncertainty
- **0**: No reliable sources found or complete contradiction

Begin your validation by conducting strategic web searches and then provide the comprehensive validation report in the specified JSON format.`;
}